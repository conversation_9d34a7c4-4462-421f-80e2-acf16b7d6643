# <PERSON>
library(optparse)
library(ggplot2)
library(dplyr)
library(RColorBrewer)
library(viridis)

# options list for parser options
option_list <- list(
    make_option(c("-a","--intersection_summary"), type="character", default=NULL,
            help="ecDNA-exon intersection summary TSV file",
            dest="intersection_summary"),
    make_option(c("-b","--output_plot"), type="character", default=NULL,
            help="Output plot PDF file",
            dest="output_plot")
)

parser <- OptionParser(usage = "%prog -a intersection_summary.tsv -b output_plot.pdf", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}

create_ecdna_exon_plots = function(summary_df) {

# Define colors
tumor_colors <- c("primary" = "#E31A1C", "recurrent" = "#1F78B4")

# 1. Number of exons per ecDNA
p <- ggplot(summary_df, aes(x = sample_name, y = n_exons_per_ecdna)) +
geom_bar(stat = "identity", color='black') +
geom_text(aes(label = n_exons_per_ecdna), vjust = -0.5) +

labs(title = "Number of exons intersecting ecDNA per sample", y = "Number of exons") +

plot_theme() +
theme(axis.text.x = element_text(angle = 90, hjust = 1, vjust = 0.5))

print(p)

# 2. Distribution of exon sizes in ecDNA
if("exon_size" %in% colnames(summary_df)) {
    p <- ggplot(summary_df, aes(x = exon_size, fill = sample_name)) +
    geom_histogram(bins = 20, alpha = 0.7) +
    
    labs(title = "Distribution of exon sizes in ecDNA", x = "Exon size (bp)", y = "Count") +
    
    plot_theme()
    
    print(p)
}

# 3. Genes most frequently found in ecDNA
if("gene" %in% colnames(summary_df)) {
    gene_counts <- summary_df %>%
        count(gene, sort = TRUE) %>%
        head(20)
    
    p <- ggplot(gene_counts, aes(x = reorder(gene, n), y = n)) +
    geom_bar(stat = "identity", color='black') +
    coord_flip() +
    
    labs(title = "Top 20 genes most frequently found in ecDNA", x = "Gene", y = "Frequency") +
    
    plot_theme()
    
    print(p)
}

return()
}

pdf(opt$output_plot)

# Load data
summary_df <- read.csv(opt$intersection_summary, sep='\t')

# Create plots
create_ecdna_exon_plots(summary_df)

dev.off()
