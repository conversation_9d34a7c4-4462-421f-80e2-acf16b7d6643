# Alec Bahcheli
import argparse
import pandas as pd
import os

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--intersected_bed", required=True)  # Bedtools intersection output
    parser.add_argument("--ecdna_results", required=True)  # Combined ecDNA results
    parser.add_argument("--output_file", required=True)  # Output file path
    return parser.parse_args()

def load_intersection_results(intersected_bed_file):
    """Load bedtools intersection results"""
    if not os.path.exists(intersected_bed_file):
        return pd.DataFrame()
    
    # Load bedtools intersection output
    df = pd.read_csv(intersected_bed_file, sep='\t', header=None)
    if df.empty:
        return pd.DataFrame()
    
    # Assign column names based on expected bedtools output
    gene_cols = ['gene_chr', 'gene_start', 'gene_end', 'gene_name']
    ecdna_cols = ['ecdna_chr', 'ecdna_start', 'ecdna_end', 'ecdna_id_sample', 'ecdna_coverage', 'ecdna_strand']
    
    # Adjust based on actual number of columns
    if len(df.columns) >= len(gene_cols) + len(ecdna_cols):
        df.columns = gene_cols + ['extra_' + str(i) for i in range(len(df.columns) - len(gene_cols) - len(ecdna_cols))] + ecdna_cols
    else:
        df.columns = ['col_' + str(i) for i in range(len(df.columns))]
    
    return df

def load_ecdna_results(ecdna_results_file):
    """Load combined ecDNA results"""
    return pd.read_csv(ecdna_results_file, sep='\t')

def process_intersections(intersection_df, ecdna_df):
    """Process intersection results to create gene-ecDNA associations"""
    if intersection_df.empty or ecdna_df.empty:
        return pd.DataFrame()
    
    # Extract circ_id and sample from ecdna_id_sample column
    intersection_df[['circ_id', 'sample_name']] = intersection_df['ecdna_id_sample'].str.split('_', n=1, expand=True)
    intersection_df['circ_id'] = pd.to_numeric(intersection_df['circ_id'], errors='coerce')
    
    # Create patient column
    intersection_df['patient'] = intersection_df['sample_name'].str.split('-').str[0]
    
    # Calculate relative coordinates within ecDNA
    intersection_df['gene_start_in_ecdna'] = intersection_df['gene_start'] - intersection_df['ecdna_start']
    intersection_df['gene_end_in_ecdna'] = intersection_df['gene_end'] - intersection_df['ecdna_start']
    
    # Get ecDNA size information from original results
    ecdna_size_info = ecdna_df.groupby(['sample_name', 'circ_id']).agg({
        'size_bp': 'first'
    }).reset_index()
    
    # Merge with size information
    result_df = pd.merge(
        intersection_df,
        ecdna_size_info,
        on=['sample_name', 'circ_id'],
        how='left'
    )
    
    # Group by patient and circ_id to get all genes per ecDNA
    grouped_results = []
    
    for (patient, circ_id), group in result_df.groupby(['patient', 'circ_id']):
        # Get basic ecDNA information
        sample_name = group['sample_name'].iloc[0]
        size_bp = group['size_bp'].iloc[0]
        
        # Collect unique genes and their coordinates
        genes_dict = {}
        for _, row in group.iterrows():
            gene_name = row['gene_name']
            if gene_name not in genes_dict:
                genes_dict[gene_name] = {
                    'gene_name': gene_name,
                    'gene_chr': row['gene_chr'],
                    'gene_start': int(row['gene_start']),
                    'gene_end': int(row['gene_end']),
                    'gene_start_in_ecdna': int(row['gene_start_in_ecdna']),
                    'gene_end_in_ecdna': int(row['gene_end_in_ecdna'])
                }

        genes_info = list(genes_dict.values())

        # Create comma-separated lists
        gene_names = ','.join([g['gene_name'] for g in genes_info])
        gene_coords = ','.join([f"{g['gene_name']}:{g['gene_start']}-{g['gene_end']}" for g in genes_info])
        gene_coords_in_ecdna = ','.join([f"{g['gene_name']}:{g['gene_start_in_ecdna']}-{g['gene_end_in_ecdna']}" for g in genes_info])
        
        grouped_results.append({
            'patient': patient,
            'circ_id': circ_id,
            'sample_name': sample_name,
            'size_bp': size_bp,
            'n_genes': len(genes_info),
            'gene_names': gene_names,
            'gene_coordinates': gene_coords,
            'gene_coordinates_in_ecdna': gene_coords_in_ecdna
        })
    
    return pd.DataFrame(grouped_results)

def main():
    """Main function"""
    args = parse_arguments()
    
    # Load input files
    intersection_df = load_intersection_results(args.intersected_bed)
    ecdna_df = load_ecdna_results(args.ecdna_results)
    
    if intersection_df.empty:
        empty_df = pd.DataFrame(columns=[
            'patient', 'circ_id', 'sample_name', 'size_bp', 'n_genes',
            'gene_names', 'gene_coordinates', 'gene_coordinates_in_ecdna'
        ])
        empty_df.to_csv(args.output_file, sep='\t', index=False)
        return
    
    # Process intersections
    result_df = process_intersections(intersection_df, ecdna_df)
    
    if result_df.empty:
        empty_df = pd.DataFrame(columns=[
            'patient', 'circ_id', 'sample_name', 'size_bp', 'n_genes',
            'gene_names', 'gene_coordinates', 'gene_coordinates_in_ecdna'
        ])
        empty_df.to_csv(args.output_file, sep='\t', index=False)
        return
    
    # Sort results
    result_df = result_df.sort_values(['patient', 'circ_id'])
    
    # Create output directory if needed
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Save results
    result_df.to_csv(args.output_file, sep='\t', index=False)

if __name__ == "__main__":
    main()
