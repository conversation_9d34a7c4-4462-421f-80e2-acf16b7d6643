# <PERSON> Ba<PERSON>cheli
import argparse
import pandas as pd

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--protein_coding_genes_gff_file", required=True)  # Input GFF file
    parser.add_argument("--protein_coding_bed_file", required=True)  # Output BED file
    return parser.parse_args()


def extract_exons_from_gff(protein_coding_genes_gff_file):
    """Extract exons from GFF file and convert to BED format"""
    # read gff file
    df = pd.read_csv(protein_coding_genes_gff_file, sep='\t', header=None, comment='#')

    # add column names
    column_names = ['seqname', 'source', 'type', 'start', 'end', 'score', 'strand', 'phase', 'attributes']
    df.columns = column_names

    # subset to only protein-coding genes
    df = df[df['attributes'].str.contains('gene_biotype=protein_coding')]

    # filter for exons only
    df = df[df['type'] == 'exon']

    # define gene name
    df['gene'] = df['attributes'].str.extract(r'gene=([^;]+)')

    # extract exon number
    df['exon_number'] = df['attributes'].str.extract(r'ID=([^;]+)').str.split('-').str[-1]

    # add chromosome with chr prefix
    df['chr'] = 'chr' + df['seqname'].str.split(".").str[0].str.split("_").str[1].str.replace(r'^0+', '', regex=True)

    # replace chr23 with chrX and chr24 with chrY
    mask = df['chr'] == 'chr23'
    df.loc[mask, 'chr'] = 'chrX'
    mask = df['chr'] == 'chr24'
    df.loc[mask, 'chr'] = 'chrY'

    # subset to chromosomes 1-22 and x and y
    valid_chrs = ['chr' + str(i) for i in range(1, 23)] + ['chrX', 'chrY']
    df = df[df['chr'].isin(valid_chrs)]

    # sort values
    df = df.sort_values(['chr', 'start'])

    # filter columns for BED format
    df = df[['chr', 'start', 'end', 'gene', 'type', 'strand']]

    return df


def main():
    """Main function to process GFF file and create exon BED file"""
    args = parse_arguments()

    # extract exons from gff
    exon_df = extract_exons_from_gff(args.protein_coding_genes_gff_file)

    # save to file
    exon_df.to_csv(args.protein_coding_bed_file, sep='\t', index=False, header=False)


if __name__ == "__main__":
    main()




