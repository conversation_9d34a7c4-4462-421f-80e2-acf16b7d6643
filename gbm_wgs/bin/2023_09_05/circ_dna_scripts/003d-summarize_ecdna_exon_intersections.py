# <PERSON> Ba<PERSON>cheli
import argparse
import pandas as pd
import os
import glob

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--intersection_dir", required=True)  # Directory containing intersection results
    parser.add_argument("--sample_list", required=True)  # Comma-separated list of samples
    parser.add_argument("--summary_file", required=True)  # Output summary file
    return parser.parse_args()


def process_sample_intersections(sample_name, intersection_dir):
    """Process intersection results for a single sample"""
    sample_dir = os.path.join(intersection_dir, sample_name)
    
    if not os.path.exists(sample_dir):
        return pd.DataFrame()
    
    # find all circ_id BED files for this sample
    bed_files = glob.glob(os.path.join(sample_dir, f"{sample_name}_circ_*_exons.bed"))
    
    all_intersections = []
    
    for bed_file in bed_files:
        # extract circ_id from filename
        filename = os.path.basename(bed_file)
        circ_id = filename.split('_circ_')[1].split('_exons.bed')[0]
        
        # read BED file
        if os.path.getsize(bed_file) > 0:
            df = pd.read_csv(bed_file, sep='\t', header=None)
            df.columns = ['chr', 'start', 'end', 'gene', 'type', 'strand']
            
            # add metadata
            df['sample_name'] = sample_name
            df['circ_id'] = circ_id
            df['exon_size'] = df['end'] - df['start']
            
            all_intersections.append(df)
    
    if all_intersections:
        return pd.concat(all_intersections, ignore_index=True)
    else:
        return pd.DataFrame()


def create_summary_statistics(combined_df):
    """Create summary statistics from combined intersection data"""
    if combined_df.empty:
        return pd.DataFrame()
    
    # summary per sample
    sample_summary = combined_df.groupby('sample_name').agg({
        'gene': 'count',
        'circ_id': 'nunique',
        'exon_size': ['mean', 'median', 'sum']
    }).reset_index()
    
    # flatten column names
    sample_summary.columns = ['sample_name', 'n_exons_per_ecdna', 'n_ecdnas_with_exons', 
                             'mean_exon_size', 'median_exon_size', 'total_exon_size']
    
    # add patient and tumor type
    sample_summary['patient'] = sample_summary['sample_name'].str.split('-').str[0]
    sample_summary['tumor_type'] = sample_summary['sample_name'].str.split('-').str[1]
    
    return sample_summary


def main():
    """Main function to summarize ecDNA-exon intersections"""
    args = parse_arguments()
    sample_list = args.sample_list.split(',')
    
    all_intersections = []
    
    # process each sample
    for sample in sample_list:
        sample_df = process_sample_intersections(sample, args.intersection_dir)
        if not sample_df.empty:
            all_intersections.append(sample_df)
    
    # combine all results
    if all_intersections:
        combined_df = pd.concat(all_intersections, ignore_index=True)
        
        # create summary statistics
        summary_df = create_summary_statistics(combined_df)
        
        # save summary
        summary_df.to_csv(args.summary_file, sep='\t', index=False)
        
        # also save detailed results
        detailed_file = args.summary_file.replace('.tsv', '_detailed.tsv')
        combined_df.to_csv(detailed_file, sep='\t', index=False)
    else:
        # create empty summary
        empty_summary = pd.DataFrame(columns=['sample_name', 'n_exons_per_ecdna', 'n_ecdnas_with_exons', 
                                            'mean_exon_size', 'median_exon_size', 'total_exon_size',
                                            'patient', 'tumor_type'])
        empty_summary.to_csv(args.summary_file, sep='\t', index=False)


if __name__ == "__main__":
    main()
