# <PERSON> Bahcheli
import argparse
import pandas as pd
import os
import subprocess

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--exon_bed_file", required=True)  # Input exon BED file
    parser.add_argument("--ecdna_bed_file", required=True)  # Input ecDNA BED file
    parser.add_argument("--sample_name", required=True)  # Sample name
    parser.add_argument("--output_dir", required=True)  # Output directory for per-circ_id BED files
    return parser.parse_args()


def intersect_with_bedtools(exon_bed_file, ecdna_bed_file, output_file):
    """Use bedtools to intersect exon BED with ecDNA BED"""
    cmd = [
        "bedtools", "intersect",
        "-a", exon_bed_file,
        "-b", ecdna_bed_file,
        "-wa", "-wb"
    ]
    
    with open(output_file, 'w') as f:
        subprocess.run(cmd, stdout=f, check=True)


def process_intersections(intersection_file, sample_name, output_dir):
    """Process bedtools intersection results and create per-circ_id BED files"""
    # read intersection results
    df = pd.read_csv(intersection_file, sep='\t', header=None)
    
    # assign column names
    # First 6 columns are from exon BED: chr, start, end, gene, type, strand
    # Next 8 columns are from ecDNA BED: chr, start, end, circ_id, fragment_id, strand, coverage, estimated_proportions
    df.columns = ['exon_chr', 'exon_start', 'exon_end', 'gene', 'type', 'exon_strand',
                  'ecdna_chr', 'ecdna_start', 'ecdna_end', 'circ_id', 'fragment_id', 'ecdna_strand', 'coverage', 'estimated_proportions']
    
    # group by circ_id
    for circ_id, group in df.groupby('circ_id'):
        # get the minimum start position for this circ_id to use as reference
        circ_start = group['ecdna_start'].min()
        
        # create relative coordinates for exons
        group_copy = group.copy()
        group_copy['relative_start'] = group_copy['exon_start'] - circ_start
        group_copy['relative_end'] = group_copy['exon_end'] - circ_start
        
        # create output BED file for this circ_id
        output_bed = group_copy[['exon_chr', 'relative_start', 'relative_end', 'gene', 'type', 'exon_strand']]
        
        # save to file
        output_file = os.path.join(output_dir, f"{sample_name}_circ_{circ_id}_exons.bed")
        output_bed.to_csv(output_file, sep='\t', index=False, header=False)


def main():
    """Main function to intersect ecDNA with exons and create relative coordinate BED files"""
    args = parse_arguments()
    
    # create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    # create temporary intersection file
    temp_intersection = os.path.join(args.output_dir, f"{args.sample_name}_temp_intersection.tsv")
    
    # intersect exons with ecDNA regions using bedtools
    intersect_with_bedtools(args.exon_bed_file, args.ecdna_bed_file, temp_intersection)
    
    # process intersections and create per-circ_id BED files
    process_intersections(temp_intersection, args.sample_name, args.output_dir)
    
    # clean up temporary file
    os.remove(temp_intersection)


if __name__ == "__main__":
    main()
