# Alec Ba<PERSON>cheli - <EMAIL>

###################################
# ecDNA exon intersection analysis
###################################

# Extract exons from GFF file and convert to BED format
rule extract_exons_from_gff:
    input:
        protein_coding_genes_gff_file = REF_DATA_DIR + "/GCF_000001405.40_GRCh38.p14_genomic.protein_coding_genes.gff",
        script = BIN_DIR + "/circ_dna_scripts/003a-process_coding_exons.py"

    output:
        exon_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/protein_exons.bed"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'
        
    shell:
        "{PYTHON} {input.script} --protein_coding_genes_gff_file {input.protein_coding_genes_gff_file} --protein_coding_bed_file {output.exon_bed_file}"


# Intersect exon BED file with each sample's ecDNA regions using bedtools
rule intersect_ecdna_exons:
    input:
        exon_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/protein_exons.bed",
        ecdna_bed_file = RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/reconstruct.ecDNA.filtered.bed"

    output:
        intersected_file = RES_DIR + "/analysis_nanopore/ecdna/exon_intersections/{sample}/exon_intersections.bed"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a {input.exon_bed_file} -b {input.ecdna_bed_file} -wa -wb > {output.intersected_file}"""


###################################
# Test rules for local testing
###################################

# Summarize ecDNA-exon intersections across all samples
rule summarize_ecdna_exon_intersections:
    input:
        intersection_dirs = expand(RES_DIR + "/analysis_nanopore/ecdna/exon_intersections/{sample}/", sample=sample_list),
        script = BIN_DIR + "/circ_dna_scripts/003d-summarize_ecdna_exon_intersections.py"

    output:
        summary_file = RES_DIR + "/analysis_nanopore/ecdna/exon_intersections/ecdna_exon_intersection_summary.tsv"

    params:
        sample_list = ",".join(sample_list),
        intersection_dir = RES_DIR + "/analysis_nanopore/ecdna/exon_intersections/"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        "{PYTHON} {input.script} --intersection_dir {params.intersection_dir} --sample_list {params.sample_list} --summary_file {output.summary_file}"


# Visualize ecDNA-exon intersection results
rule visualize_ecdna_exon_intersections:
    input:
        summary_file = RES_DIR + "/analysis_nanopore/ecdna/exon_intersections/ecdna_exon_intersection_summary.tsv",
        script = BIN_DIR + "/circ_dna_scripts/003c-visualize_ecdna_exon_intersections.R"

    output:
        output_plot = RES_DIR + "/analysis_nanopore/ecdna/_figures/ecdna_exon_intersections.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        "{RSCRIPT} {input.script} --intersection_summary {input.summary_file} --output_plot {output.output_plot}"
