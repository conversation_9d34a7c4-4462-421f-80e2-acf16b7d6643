# Reimand Lab Notebook

## 2020

### September

#### 03

I followed the guidelines on the two Reimand lab wiki pages:

- https://wiki.oicr.on.ca/display/reimandlab/2020/04/22/Guideline+to+using+the+R+stats+programming+environment+on+the+OICR+HPC

- https://wiki.oicr.on.ca/display/reimandlab/Using+the+R+programming+environment+on+the+HPC+with+JupyterLab

Conda (version 4.8.4) was installed in /.mounts/labs/reimandlab/private/users/abahcheli and paths added to .bashrc. A medaka environment was created for sequencing data analysis: ```conda create -n medaka -c conda-forge -c bioconda medaka```

Under the medaka environment, the following packages were installed:

- minimap2
- bowtie2
- samtools

Easier access to chickenwire was achieved by adding my ssh public key to the chickenwire server.

Jupyter notebook was installed in the under the conda r_env environment, as was R.

A consequence of this was the establishmnent of an R directory in /u/abahcheli on the chickenwire server. However, this directory was less than 1Gb.

A private github repository was established for Reimand lab work (reimand_lab_ab). The repo was setup to contain this lab notebook and a brief readme.md along with any work (not data) that will be completed in the Reimand lab.

To access the Jupyter notebook, run a compute node (it does not have to have internet access). In a conda environment of that node, run: ```jupyter-lab --no-browser --port=8889 --ip=0.0.0.0```.

To port-forward from chicken wire to your local computer, ```ssh -L 8889:[compute_node].hpc.oicr.on.ca:8889 [username]@chickenwire.oicr.on.ca```

Access the Jupyter Notebook through http://127.0.0.1:8889/lab.

#### 14

The remainder of the previous week was spent getting access to OICR, completing required orientations and working on Molecular Genetics courses.

A rough draft of CGS-D (Canadian Graduate Scholarship - Doctoral) abstract and title was written and sent to Jüri for review on Friday, 11 September 2020. This was submitted to Stephanie Baello-Ziobrowski at OICR today.

Nucleotide sequences of human ion channels from the refseq database were retrieved:

```(ion channel) AND "Homo sapiens"[porgn:__txid9606] AND srcdb_refseq[PROP]``` under ```https://www.ncbi.nlm.nih.gov/nuccore```

A second database was provided in a spreadsheet format (ION_CHANNELS_targets_and_families.xlsx) that is now contained in the reimand_lab_ab github repository.

Access to the ion channel data was obtained: ```/.mounts/labs/reimandlab/private/generated_raw_data/TCGA_PanCanAtlas```

mRNA abundance data was also provided:
```unc.edu_PANCAN_IlluminaHiSeq_RNASeqV2.geneExp_whitelisted.tsv```

Barcodes reveal information about the tissue type, which can be resolved using information [here](https://gdc.cancer.gov/resources-tcga-users/tcga-code-tables/tissue-source-site-codes). 

"Some data loading procedures will alter some codes that start with numbers, watch out (https://docs.gdc.cancer.gov/Encyclopedia/pages/TCGA_Barcode/)"

#### 15

The tasks for the next week are as follows:
- Establish a fasta-format database of non-redundant ion channel transcript sequences
- Get the transcript information for ion channel genes from each database
- Get the distribution and descriptive statistics of ion channels
- Identify distribution-fitting model
- Normalize by log-ratio transform
- Apply different statistics packages (eg EdgeR)
- Use Machine-Learning Classifier to identify outliers

An initial interpretation of the transcript counts data suggested that the genes are labelled by Human Genome Nomenclature (HGNC) name and ID in the format *[name]|[ID]*.

The [HGNC site](https://www.genenames.org/download/custom/) contained 47299 named and identified genes as of this date. All gene names and IDs associated with *channels* were extracted, totalling 310 genes (versus the 285 on the spreadsheet provided by Juri). 

The R kernel kept resetting in the Jupyter Notebook. An R environment was created with: ```conda create -n r_env r-essentials r-base``` 

The R kernel was installed with: ```conda install -c r r-irkernel```

Next, the jupyter notebook was created: ```conda install -c conda-forge jupyterlab```

Within conda's r_env environment, the following packages were installed as dependencies of IRKernel:
- repr
- IRdisplay
- evaluate
- crayon
- pbdZMQ
- devtools
- uuid
- digest

Finally, the user-specific IRkernel was established:
```R
IRkernel::installspec()
```

To obtain relevant data from the transcript read counts, python and shell scripts (extract_relevant_data.py and ion_channel_processing.sh) were written that extract the relevant ion channel information from the transcript read counts dataset. 

#### 16

Running __ion_channel_processing.sh__ processed the data in ~3 minutes to contain only ion channel data and cancer types of interest (requires *extract_relevant_data.py*). 253 of the possible 310 (81.6%) of the channel-associated genes annotated in HGNC were contained in the PANCAN_IlluminaHiSeq_RNASeq dataset. 

The dataset is therefore 253 genes of interest with 10327 samples. This was log-ratio transformed using the formula __y = log(x) - mean(log(x))__ then analyzed on a histogram and boxplot. 

#### 17 

It was realized that the 10327 samples contains samples from all tissue sites, not just gliomas. The __ion_channel_processing.sh__ data processing script was modified to specify cancers of interest (gliomas). 

The final, properly filtered dataset contained 249 genes of interest (80%) in 186 samples. The processing was sped up to ~80 seconds from ~3 minutes. 

I began looking at the distribution of data. Total ion channel counts between tissue samples is almost normal, with a max number of counts of 152643.89 and a minimum number of counts of 89494.15. The 5% and 95% quantiles are 96660.93 and 140476.32, respectively. Data between the 5% and 95% quantiles were used for analysis.

#### 21

The following tasks from the previous week were not completed or deemed still relevant:
- Apply different statistics packages (eg EdgeR)
- Use Machine-Learning Classifier to identify outliers

The specific steps that must be followed to complete these goals are as follows: 
- Separate each glioma cancer into its own dataset of only ion channel genes for individual analysis

- Identify ion channel outliers within a specific glioma type, start with ion channels (IC) that are over-expressed only:
    - Look for samples with gene FC > 1.5 * IQR
    - Determine the ratio of samples that contain high (outlier) gene expression for a specific IC compared to samples that are normal for that type
    - Calculate the median expression for outlier samples in the specific IC and samples without outliers in the IC; determine a good assessment of fold change (FC)

- Calculate 10,000x series of random sample sets (protein coding genes) and calculate the FC_r (fold change of random set) of outliers in those genes
    - Calculate how many of those samples have genes that are outliers (N_r)

- Calculate summary statistics:
    - sum(FC_r >= FC) / 10000
    - sum(N_r >= N) / 10000

#### 23 

Yesterday and today was spent identifying the median expression levels of over-expression outliers and non-outliers (based on the >1.5x median outlier definition). Ratios of the samples containing outliers for each gene was also calculated.

The next step is to sample all genes and samples randomly for the "normal" distribution of expression levels of protein-coding genes. To start, we have to specify data from protein-coding genes only. The __extract_relevant_data.py__ script was modified to specify the field "gene with protein product", as recommended by the HGNC site. 

#### 24

Replicating random sampling and outputting the mean rate of IQR outliers and the mean FC of those outliers was calculated. As a result, the R Jupyter notebook was finished for the purposes of an initial analysis.

A new R script was written to automate the sampling of data and analysis of the cancer-specific data. 

qsub -P reimandlab -l h_vmem=15G -b y -e errors.txt -o nohup.txt -N ion_channel_analysis /bin/bash /.mounts/labs/reimandlab/private/users/abahcheli/execute_script.sh

JOB: 22500691

/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/ic_sample_fc_iqr.r -r 10000 -c /.mounts/labs/reimandlab/private/users/abahcheli/ion_channels_data/ion_channels.csv -i /.mounts/labs/reimandlab/private/users/abahcheli/ion_channels_data/PANCAN_IlluminaHiSeq_RNASeqV2_proteincodinggenes.csv -s /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/reference_genes/cancers_of_interest.tsv -o /.mounts/labs/reimandlab/private/users/abahcheli/ion_channels_results


#### 25

Installed data.table and ggplot2 via conda. Make sure to install all R packages using conda: eg```conda install -c r r-ggplot2```

The initial data analysis script was modified to sample_iqr_fc.r with improvements such as data.table to increase the speed of analysis. 

Execution of the data analysis script was also added to the end of the ion_channel_processing.sh script to execute all analysis from raw data at once.

#### 28

The data was analyzed and the proper execution of the scripts were verified. 

After the meeting the Jüri, the following goals were set for the week:

- Learn statistics
- Summarize ion channel initial analysis results

Job: 22517254

qsub -P reimandlab -l h_vmem=20G -b y -e errors.txt -o nohup.txt -N ion_channel_analysis /bin/bash /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/ion_channel_processing.sh

16587 genes (total possible 19620 genes) from the transcriptomic dataset were protein-coding. Of these, 246 were ion channels (total possible 310 ion channels). We selected 10,000 combination from a possible 16587 C 246 = 16587! / ((16587 - 246)!246!). 


### October
#### 2

The rest of this week was spent learning and practicing statistics and applied math. 

#### 5

The z-scores for the ion channel FC and IQR-outlier ratios for both gbm and lgg were calculated and depicted on a scatterplot of IQR-outlier ratio versus FC. The results suggest that both gbm and lgg have significantly upregulated IC genes with FC p-values of 6.953e-12 and 4.653e-12, respectively and IQR p-values of 2.498e-14 and 2.47e-13, respectively. 

Next is to separate ion channels genes by family and group to determine which families are up-regulated. 

The different families of ion channels can be identified by investigating the 246 ion channel protein-coding genes identified in the transcriptomic data. Families include but are not limited to:

- Ligand-gated ion channels
- Voltage-gated ion channels

#### 6

Began reading the details on applications of elastic net algorithm in R. 

Worked and focussed primarily on the CGS-D application. 

#### 7

Continued reading the [details on applications](https://daviddalpiaz.github.io/r4sl/elastic-net.html) of elastic net algorithm in R.


Permutation tests

Survival depends on status (dead, alive, condition) and time (since diagnosis)
    - Cox proportional hazard model (Coxph)
    - median dichotomization

#### 19

There was an issue with the organization of clinical information in the __clinical_PANCAN_patient_with_followup.tsv__ file where some entries were separated into two different lines. We can identify patients from the original trial using the __patient_id__ column (column 27, if the first column is indexed as 1).

We need to pre-process the data before applying elastic net (EN) analysis in R, because the clinical information is incomplete and cannot be ready efficiently into R. The script extracts the clinical information specifically for glioma samples. 

Categories of interest in the clinical PANCAN with followup file is (indexed at 1):
- days_to_death  7
- days_to_initial_pathologic_diagnosis  8
- age_at_initial_pathologic_diagnosis  9
- vital_status (ie Dead or Alive)  5
- gender  3

Now that we know which categories are important, we're going to want to extract the clinical information from patients that are relevant.

#### 20

The extracting clinical data script was modified to pair clinical data with transcriptomic data. The output has both transcriptomic data and clinical data samples in rows and separates results into files marked by cancer name according to unique cancer names from the cancer information file (file containing cancer codes).

A rough draft of the elastic net script adapted from Jüri's script was made.

#### 21

The rough draft was corrected to be functional and run for 1000 iterations of EN. 

```bash

qsub -P reimandlab -l h_vmem=10G -pe smp 15 -N en_script /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/ic_en.sh h_rt=259200 -o /u/abahcheli/en_output -e /u/abahcheli/en_error

```

ion_channel_processing.sh was re-named to ic_initial.sh.

#### 22

The elastic net process was run for GBM, LGG and PG (see previous day's code). This had to be re-run following correction of the code to eliminate negative values for days_to_death.

GBM had no ion channel predictors that were expressed in more than 50% of the 1,000 trials. However, it saw some with 5% prevalence.

LGG had 27 ion channel predictors identified in >50% of the 1,000 trials. These were analyzed for statistical significance.

#### 26

Kaplan meier curves by each median-dichotomized predictor identified were created in the LGG dataset. 

Concordance indices for test and train data were generated for the identified LGG predictors. Briefly a Wald test was run with pair of coefficients with absolute values less than 0.1, 0.25 and 0.5 with results as follows, respectively:
- X2 = 0.25, df = 5, P(> X2) = 1.0
- X2 = 2.7, df = 10, P(> X2) = 0.99
- X2 = 21.4, df = 20, P(> X2) = 0.37

#### 27

Kaplan meier curves for statistical signifcance showed no difference in a median dichotomization of ion channel expression. 

Concordance indices for median-dichotomized ion channels in a CoxPH model against the training and test sets were 0.6732775 and 0.5561497, respectively. 

Next steps include:
- Use a continuous distribution for the CoxPH model instead of median-dichotomization of variables
- Look at the distribution of prognostic ion channels and identify any outliers that may throw off the distribution; use a log-ratio transform to get relative expressions
- Identify which ion channels show the highest prognostic-ability 
- Separate Kaplan Meier curves into eg. quartiles 
- Look for mutations in these ion channels

#### 28


__A Brief Update on Cancer Databases__

*TCGA*
The Cancer Genome Atlas (TCGA), a project of the National Cancer Institute and the National Human Genome Research Institute, oversees the generation of genomic data from quality-controlled samples, most of which have been analyzed using multiple platforms. 

This repository continuously updates data as it becomes available. Data has already been processed.

*ICGC*
The International Cancer Genome Consortium (ICGC) is a one-stop-shopping portal through which you can access data from its 12 member countries, as well as from the TCGA and COSMIC databases.

This data is not quality-controlled and is updated on a monthly basis. Data has already been processed.

*COSMIC*
UK Cancer Genome Project houses a data collection called COSMIC, the single most comprehensive catalog of somatic mutations in the world.

*ENSEMBL*
Ensembl is a genome browser for vertebrate genomes that annotates genes, computes multiple alignments, predicts regulatory function and collects disease data. Ensembl tools include BLAST, BLAT, BioMart and the Variant Effect Predictor (VEP) for all supported species.

*Expassy*
Swiss bioinformatics tool and resource portal. Contains GUI bioinformatics tools in addition to links to various databases (eg Uniprot for protein sequence and functional information and RCSB / Protein Data Bank containing structural information for different proteins). 

#### 29

Concordance of CoxPH model on non-median dichotomized data was not improved for the testing data (0.3840206) but slightly improved for the training data (0.66236).

Note that 225 transcript measurements of ion channels from all LGG samples were 0 (225 / 4158 = 5%). Log ratio transforming about the mean after adding 0.5 to all counts (correct for 0). 

Log ratio transform:
```R
dataset_lrt = data.frame(apply((data + 0.5), 2, function(x) log(x) - mean(log(x)) ))
```

Concordance of CoxPH models on log-ratio transformed data was improved for both training and testing sets with C-indices of 0.7109662 and 0.5313433, respectively. 


#### 30 

To look at prognostic indication of combinations of ion channels look at ion-channel family expressions.

None of the families showed any improved prognostic ability when summing the central log-ratio data of ion channel family members. 

calcium voltage-gated channel
CAC
0.5540052 0.4123711

cation channel sperm associated
CAT
0.5348837 0.7113402

chloride voltage-gated channel
CLC
0.5381568 0.5335052

chloride intracellular channel
CLI
0.5130060 0.6443299

hyperpolarization activated cyclic nucleotide gated potassium
HCN
0.5478036 0.3891753

potassium channel
KCN
0.5881137 0.5309278

potassium channel tetramerization domain
KCT
0.5521102 0.5927835

polycystin transient receptor potential channel
PKD
0.5465978 0.4097938

sodium channel
SCN
0.5602067 0.4716495

transient receptor potential cation channel
TRP
0.5355728 0.4252577

### November
#### 02

Also looked at the individual ion channels with the best prognostic ability (highest coefficients) in combination with each other on a Kaplan Meier plot. 

I used the clrt data because that was the best at predicting prognostic outcomes. The ion channels with the best prognostic ability (top 50% of coefficients from CoxPH on clrt data) were: 

CACNG5.27091 CATSPER2.117155     CLCNKA.1187      HCN3.57657    KCNIP1.30820 
    0.19464905      0.39902481      0.14169795      0.19848034      0.18819454 
KCNIP3.30818    KCNK17.89822      KCNK5.8645      KCNN2.3781      KCNN4.3783 
    0.13351744      0.24073076      0.23740950      0.12887386      0.51794248 
KCTD18.130535     SCNN1G.6340     TRPM8.79054 
    1.07420198      0.10425085      0.08258415 

Km plots were made by splitting pairwise combinations of these ion channels into groups based on which of the ion channels showed an increase or decrease in clrt data. None of the km plots by any combinations of these ion channels data were significant. 

It was realized that I had used the GBM transcriptomic data and patient outcomes with LGG prognostic indicators. The following are a summary of the results for the prognostic outcomes corrected for LGG.

__LGG Prognostic Factors from EN Analysis__

Concordance indices for median-dichotomized ion channels in a CoxPH model against the training and test sets were 0.8239112 and 0.7490145, respectively. 

Concordance indices for CoxPH against central log-transformed data training and test data were 0.8512677 and 0.7634691, respectively. 

For ion channel families:

calcium voltage-gated channel
CAC
0.4995754 0.5111695

cation channel sperm associated
CAT
0.5903797 0.6373193

chloride voltage-gated channel
CLC
0.6889482 0.6018397

chloride intracellular channel
CLI
0.7161834 0.7490145

hyperpolarization activated cyclic nucleotide gated potassium
HCN
0.6410288 0.6176084

potassium channel
KCN
0.5486473 0.7516426

potassium channel tetramerization domain
KCT
0.7271624 0.6938239

polycystin transient receptor potential channel
PKD
0.7403858 0.6780552

sodium channel
SCN
0.6519471 0.5650460

transient receptor potential cation channel
TRP
0.7878806 0.7450723

The ion channels with the best prognostic ability (top 25% of coefficients from CoxPH on central-log transformed data) were: 

CLCN5.1184 0.7040239
CLCNKA.1187 0.2754868
CLIC3.9022 0.2336116
HCN3.57657 0.2590047
KCNIP1.30820 0.2255435
PKD2.5311 0.4291380
TRPV3.162514 0.4879100


#### 03

Began writing a script to apply individual analysis separate to all ion channels. 

#### 04

A new method for ssh tunelling was revealed by Masroor for instances where the single ssh method is not working. 

1. Login to chickenwire, then hpc (ugehn). Activate conda and run jupyter-lab with port forwarding as expected (eg. for port 8889).

2. Open another terminal, do port-forwarding to chickenwire using the following command:

```bash
ssh -f <username>@chickenwire.oicr.on.ca -L 8889:localhost:8889 -N
```

3. Login to chickenwire then do port-forwarding to a specific computer node as defined in the first step:

```bash
ssh <username>@ugehn.hpc.oicr.on.ca -L 8889:<ugehn_node>.hpc.oicr.on.ca:8889 -N -K
```
4. Shutdown the port forwarding when you are done by searching running programs

```bash
ps aux | grep ssh
```

__Alternative / Original Method__

```bash
ssh -L 8889:[compute_node].hpc.oicr.on.ca:8889 [username]@chickenwire.oicr.on.ca
```


#### 05

[Survival analysis in R.](https://www.datacamp.com/community/tutorials/survival-analysis-R)

__Notes on Cox Proportional Hazard Models__

Found a good explanation of the [approaches to survival models](https://data.princeton.edu/wws509/notes/c7s3). 

The validity and significance of your Cox prognostic model depends on both its calibration and ability to discriminate. Calibration is effectively how well the estimates or predictions of a survival model can be applied between derivation and validation datasets. Calibration is how applicable the model is between samples. 

Discrimination reflects how well your prognostic index can discriminate between "risk groups" relative to what the actual outcomes are. Discrimination is effectively how well the model can accurately predict outcomes (a measure of discrimination is concordance between the model's hypothesized and actual outcomes).

These results depend heavily on the baseline survival function, which is non-parametric. CoxPH is powerful and defined as a semi-parametric model by the fact that it does not explicitly require resolving the baseline survival function but instead measured the proportional hazards between two groups. 

*One way of developing a semi-parametric model without requiring the baseline survival function is by defining step-wise probabilities at discrete time intervals. This is the Kaplan Meier method of estimating the baseline survival.*

Performing a logrank of Cox test between risk groups identified from applying the CoxPH PI model to patient data in a Kaplan-Meier plot does not quantify discrimination of the survival outcomes. Instead, it measures against the null hypothesis of survival within risk groups coincide (is the survival different between groups). 

*Note that a wilcoxon test is a non-parametric test between two group of pairs to determine if the two sets of pairs are different from one-another. The test statistic, W, follows a specific distribution with no simple expression E(W) = 0.*

To better evaluate discrimination derived from the PI, calculate hazard ratios.



Another goal is to identify prognostic factors by investigating ion channels individually (using univariate analysis methods). This was done previously but apparently revealed different results than I received using the elastic net approach. I first wanted to understand what had happened to the ion channel that was experimentally validated and why it did not appear as a prognostic index in the elastic net approach.

#### 09

We found the following as significantly prognostic for LGG with different cutoffs:

__50% of runs (original results)__
CACNA2D2.9254	CACNG5.27091	CATSPER1.117144	CATSPER2.117155	CLCN5.1184	CLCNKA.1187	CLIC1.1192	CLIC3.9022	HCN3.57657	KCNIP1.30820	KCNIP3.30818	KCNK17.89822	KCNK3.3777	KCNK5.8645	KCNN2.3781	KCNN4.3783	KCNS3.3790	KCTD14.65987	KCTD18.130535	KCTD19.146212	PKD1L1.168507	PKD2.5311	SCN11A.11280	SCNN1B.6338	SCNN1G.6340	TRPM8.79054 TRPV3.162514

__30% of runs__
CACNA1B.774	CACNA2D2.9254	CACNG5.27091	CATSPER1.117144	CATSPER2.117155CLCN5.1184	CLCNKA.1187	CLIC1.1192	CLIC3.9022	HCN3.57657	KCND2.3751	KCNE4.23704	KCNH8.131096	KCNIP1.30820	KCNIP2.30819	KCNIP3.30818	KCNK17.89822	KCNK3.3777	KCNK5.8645	KCNN2.3781	KCNN4.3783	KCNS3.3790	KCTD14.65987	KCTD18.130535	KCTD19.146212	KCTD21.283219	PKD1L1.168507	PKD2.5311	SCN11A.11280	SCNN1A.6337	SCNN1B.6338	SCNN1G.6340	TRPM4.54795	TRPM8.79054	TRPV3.162514

Note that in the 10% cutoff, SCN9A is revealed. It is a significant prognostic indicator, however, it may not be as significant as others or its significance may be better reflected in the expression of another ion channel. 

__10% of runs__
CACNA1B.774	CACNA2D2.9254	CACNG5.27091	CATSPER1.117144	CATSPER2.117155CLCN5.1184	CLCNKA.1187	CLIC1.1192	CLIC3.9022	CLIC6.54102	CNGA3.1261	HCN3.57657	KCNA2.3737	KCNA3.3738	KCNA6.3742	KCNAB3.9196	KCND2.3751	KCNE1.3753	KCNE4.23704	KCNG4.93107	KCNH2.3757	KCNH8.131096	KCNIP1.30820	KCNIP2.30819	KCNIP3.30818	KCNJ10.3766	KCNJ8.3764	KCNK17.89822	KCNK3.3777	KCNK5.8645	KCNN2.3781	KCNN4.3783	KCNS3.3790	KCTD11.147040	KCTD14.65987	KCTD15.79047	KCTD18.130535	KCTD19.146212	KCTD21.283219	KCTD9.54793	NALCN.259232	PKD1.5310	PKD1L1.168507	PKD2.5311	SCN11A.11280	SCN3A.6328	SCN4A.6329	*SCN9A.6335*	SCNN1A.6337	SCNN1B.6338	SCNN1G.6340	TRPA1.8989	TRPM1.4308	TRPM4.54795	TRPM6.140803	TRPM8.79054	TRPV3.162514	UNC80.285175

__5% of runs__
CACNA1B.774	CACNA1F.778	CACNA1I.8911	CACNA2D2.9254	CACNB3.784	CACNG2.10369	CACNG5.27091	CATSPER1.117144	CATSPER2.117155	CLCN5.1184	CLCNKA.1187	CLIC1.1192	CLIC3.9022	CLIC6.54102	CNGA1.1259	CNGA3.1261	ENKUR.219670	HCN3.57657	KCMF1.56888	KCNA2.3737	KCNA3.3738	KCNA6.3742	KCNAB3.9196	KCNC1.3746	KCNC4.3749	KCND2.3751	KCNE1.3753	KCNE3.10008	KCNE4.23704	KCNF1.3754	KCNG2.26251	KCNG4.93107	KCNH1.3756	KCNH2.3757	KCNH8.131096	KCNIP1.30820	KCNIP2.30819	KCNIP3.30818	KCNJ10.3766	KCNJ11.3767	KCNJ8.3764	KCNK10.54207	KCNK12.56660	KCNK17.89822	KCNK3.3777	KCNK4.50801	KCNK5.8645	KCNK7.10089	KCNN2.3781	KCNN4.3783	KCNS1.3787	KCNS3.3790	KCNV2.169522	KCTD11.147040	KCTD14.65987	KCTD15.79047	KCTD18.130535	KCTD19.146212	KCTD21.283219	KCTD9.54793	NALCN.259232	PKD1.5310	PKD1L1.168507	PKD2.5311	SCLT1.132320	SCN11A.11280	SCN3A.6328	SCN4A.6329	SCN9A.6335	SCNN1A.6337	SCNN1B.6338	SCNN1G.6340	TRPA1.8989	TRPM1.4308	TRPM3.80036	TRPM4.54795	TRPM6.140803	TRPM7.54822	TRPM8.79054	TRPV3.162514	UNC80.285175

Identify the correlation in expression (or co-variance) between ion channels to determine which ion channel becomes more prognostic in co-expression cases. 



 [1] "coefficients"      "var"               "loglik"           
 [4] "score"             "iter"              "linear.predictors"
 [7] "residuals"         "means"             "method"           
[10] "n"                 "nevent"            "terms"            
[13] "assign"            "wald.test"         "concordance"      
[16] "y"                 "timefix"           "formula"          
[19] "call" 


#### 10

Re-ran the elastic net test with dichotomization by ic expression outliers (dichotomized each ion channel by whether expression in a sample was greater than 1.5x the IQR). Basically, expression of ion channel in a given was analyzed for whether the expression in that sample was within 1.5x IQR the distribution across samples or if it was an outlier.

Ran on a parallel environment with the same host (smp) using shared memory with n_slots.

```
qsub -P reimandlab -l h_vmem=12G,h_rt=0:12:0:0 -pe smp 10 /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/ic_en.sh
```

#### 11

The univariate investigation script was written. Briefly, it runs replicates on a 80-20 train-test split dataset that investigates which ion channels have coxph coefficients greater than a cutoff (-c). The user can also define how to dichotomize the data: -d median/outlier/logratio.

The following job was submitted for the 3 different dichotomization types:

```
qsub -P reimandlab -l h_vmem=12G,h_rt=0:12:0:0 -pe smp 15 /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/univariate1.sh
```

The results contain prognostic indicators found in 50%, 30%, 10% and 5% of the 1000 runs of univariate coxph modelling that had absolute coefficients above 0.1. 

#### 12

Started analyzing the results from the median dichotomization and outlier dichotomization results and writing a "story" in the ic_results.ipynb fie. 

One ion channels was identified as prognostic for GBM samples in at least 50% of elastic net runs when outlier dichotomizing the data: CATSPER4	cation channel sperm associated 4.

The CoxPH model built on the training data was used to fit some kind of patient data for the KM plots. The patient data and respective KM plots were either:
- All patient data combined (training and test)
- Test patient data exclusively

The same two kinds of KM plots were developed for individual prognostic factors. 

#### 13

Apparently ggplot does not recognize local (within function) variables. The variables must be defined globally or at least outside of a function. 


#### 16

Generated combined survival graphs for:
- LGG median dichotomization en-results:
    - Median dichotomized curves
    - Outlier dichotmized curves
    - Log-ratio transformed curves

- LGG outlier dichotomization en-results:
    - Median dichotomized curves
    - Outlier dichotmized curves
    - Log-ratio transformed curves

- LGG logratio transformed en-results:
    - Median dichotomized curves
    - Outlier dichotmized curves
    - Log-ratio transformed curves

There were unique results from each data transformation method used in elastic nets (see ic_results.ipynb) and combinations of unique results between strategies. 

Overall, median dichotomization identified the most prognostic factors, outlier dichotomization identified the fewest. 

PDFs of the Kaplan-Meier survival curves were generated. They were named based on: 

- The type of cancer investigated (Brain_Lower_Grade_Glioma vs Glioblastoma_multiforme)
- The elastic net results investigated beased on the data processing method (logratio vs median vs outlier) 
- The data processing method (median vs outlier vs lrt) for the CoxPH model used to fit the data for the KM survival curves

In all cases, the KM survival curves for CoxPH model fitted to the test data were immediately followed by the KM survival curves from model fitting to all data combined 

Each category was separated with a hyphen (-) in the pdf file name. For example, KM survival curves from Brain Lower Grade Glioma patients identified using median dichotomization elastic net CoxPH fitting and analyzed for the KM survival curve by fitting a CoxPH model to log ratio transformed data would be labelled __Brain_Lower_Grade_Glioma-median_en-lrt_dichot.pdf__.

#### 17

PCA of ion channels for LGG and GBM was applied. 

Generated independent survival graphs for the prognostic factors identified in the combined LGG runs using the same naming methodology. 

#### 18 

The following GBM samples had KM survival curves generated:

- GBM median dichotomization en-results (10% had 1 gene):
    - Median dichotomized curves
    - Outlier dichotmized curves
    - Log-ratio transformed curves

- GBM outlier dichotomization en-results (50% had 1 gene):
    - Median dichotomized curves
    - Outlier dichotmized curves
    - Log-ratio transformed curves

- GBM logratio transformed en-results (10% had 10 genes):
    - Median dichotomized curves
    - Outlier dichotmized curves
    - Log-ratio transformed curves

- GBM logratio transformed en-results (50% had 1 gene):
    - Median dichotomized curves
    - Outlier dichotmized curves
    - Log-ratio transformed curves


The same gene was identified as most prognostic in EN runs from median dichotomized, outlier dichotomized and log ratio transformed data:

CATSPER4.378807 cation channel sperm associated 4

The CoxPH model based on median dichotomization of this gene did not identify any separate groups in our dataset, thus yielding no KM curves.

Individual KM curves were generated for the GBM logratio transformed en-results that were identified in 10% of trials because they identified the most prognostic factors compared to any other 10% trial (10 vs 9 from outlier and 1 from median). 

#### 19

The univariate approach was modified to generate the mean, median and variance for the coefficients from the 1000 runs. The results were evaluated based on the concordance indices for the univariate coxph against the subset of data labelled test data. Results were separated by average concordance index for this test data into minimum concordance groups of: 0.7, 0.675, 0.65, 0.625, 0.6, 0.55.

For the GBM samples, none of the data processing methods identified any predictors with a concordance with test data greater than 0.6 in the univariate analysis.

For the LGG samples, the logratio identified one ion channels with concordance above 0.7 from the univariate approaches that were different from the elastic net results: NALCN.259232. Plots were made for these individually and combined.


Analyzed expression covariance between ion channels. 

Average expression covariance between ion channels is 0.085, median is 0.059. The genes clustered by hierarchical clustering were written to another file for further analysis. 


Began adding visuals of results to a google docs slideshow. Added only the data of greatest significance.

#### 20

Worked on and completed CGS-D application.

#### 23

Re-organized the ion channels directory to a more intuitive setup. Future projects will contain more intuitive directory setups and scripts, but the ion channels directory and repository will lack this detailed setup based on the fact that re-organizing the entire repository would take too long.

Reviewing what is required to generate the results from the data:

1. Execute the __ic_hgg_expression_analysis.sh__ script after ensuring that the *RSCRIPT* variable is correctly assigned to execute Rscripts. 

2. Execute the __ic_hgg_experssion_analysis.sh__ script, again, after ensuring that the *RSCRIPT* variable is correctly assigned to execute Rscripts. Also make sure that you declare the number of threads you want to use for the analysis, and make sure that these threads are available.

3. Execute the __ic_hgg_experssion_analysis.sh__ script, making sure that the *RSCRIPT* variable the number of threads you want to use for the analysis are correctly defined and available.

4. Analyze the results either with the interactive ipynb in the results directory (__ic_results.ipynb__), or by executing the __generate_figures.R__ script (in the __bin__ directory) and interpretting results in the the __results.ipynb__ (also in the __bin__ directory).

A results interpretation is contained in the file __interpretation.md__ under the results directory.


#### 24

Worked on statistics and background glioma readings. 

#### 25 

Changed the univariate and elastic net approaches to require at least 20% of training sample data to be differentially classified (ensures enough data to make meaningful / statistical inferences about the results; modify within the univariate.R and elastic_net.R scripts). 

IDH mutation status was included as a discriminating factor for figures generation (split prognostic factors by IDH mutation as well). Still have to add considerations for other non-gene prognostic factors. 

Made a master execution script to run the entire pipeline (called master_execute.sh). Ran this script to test the pipeline and the new testing and figure genetation scripts. 

Note: running a shell script from the head node did not allow the import of pandas when defining python to execute the python scripts as /usr/bin/env/ python.

```bash
qsub -P reimandlab -l h_vmem=18G,h_rt=0:12:0:0 -pe smp 10 /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/master_execute.sh

```

#### 26

An introduction for writing [LaTeX documents](https://www.overleaf.com/learn/latex/Learn_LaTeX_in_30_minutes#What_is_LaTeX.3F). 


```bash
qsub -P reimandlab -l h_vmem=12G,h_rt=0:12:0:0 -pe smp 10 /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/ic_en-001.sh

qsub -P reimandlab -l h_vmem=12G,h_rt=0:12:0:0 -pe smp 10 /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/univariate-001.sh

qsub -P reimandlab -l h_vmem=20G /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/univariate-001.sh

qsub -P reimandlab -l h_vmem=30G,h_rt=0:12:0:0 /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/master_exec.sh
```

1. Steps to download data from TCGA:

2. Go to GDC data portal

3. Click 'Data'

4. Select types of cancer and files (RNA-seq in your case)

5. Click 'Download Manifest' and save the manifest file.

6. Install 'GDC data transfer tool (gdc-client)'.

7. Go to the directory containing the manifest file.

8. In terminal (command line), type "gdc-client download -m MANIFEST_YOU_DOWNLOADED".


#### 27

- Major databases:
    - TCGA
    - Encode
    - PCAWG
    - GEO (NCBI)

Look through papers to get more databases and individuals data. 

#### 30

Körber, V. et al. Evolutionary trajectories of IDHWT glioblastomas reveal a common path of early tumorigenesis instigated years ahead of initial diagnosis.

TCGA is a subset of PCAWG: how can I identify which samples are the same in both datasets?

Finished correcting master directory script. Re-ran whole analysis process and figure generation.


### December
#### 01 

Realized that a minimum amount of sample expression data is required for generating km curves with combined prognostic factors. Subset data (train, test, patient data) to include only informative prognostic factors.

#### 02

[Distinguishing between different glioma forms.](https://www.mayfieldclinic.com/pe-glioma.htm)

Began re-writing a master script and adding better variable descriptions to the R scripts. 

1. Scripts for data modification use are labelled *000* (data processing section uses and creates *000* files while generating *001* files)
    - Reference data and modified reference data files (non-collected data) are labelled *000*

2. Scripts for analysis are labelled *001* and use *001*-labelled data (and some *002* reference data) to generate *002* results

3. Scripts for results interpretation and figure generation are labelled *002* and use *002*-labelled data to generate figures in the results/figures directory.


#### 03

Good site for access to publicly-available transcriptomic datareferences (download the data from GEO): https://hgserver1.amc.nl/cgi-bin/r2/main.cgi

PANCAN data is a subset of TCGA data (TCGA data is a subset of PCAWG data). PANCAN dataset was used in the initial ion channels study. 

Most puclibly-available data does not have clinical information.

#### 04

[Tutorial for downloading data from NCBI's ftp server.](https://www.ncbi.nlm.nih.gov/projects/Sequin/download/ftp_example.html)

The German Glioma Network does not have clinical information https://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?acc=GSE61374; /geo/series/GSE61nnn/GSE61374/matrix/. Look at different validation data.

Data that had been previously analyzed in this project was downloaded. (https://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?acc=GSE13041 and https://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?acc=GSE4271)

Collected data onto the server in the directory:
/.mounts/labs/reimandlab/private/users/abahcheli/ncbi_data

Copied from the following sites:
ftp.ncbi.nlm.nih.gov parameters: ```passive on```        AND     ```bin```
/geo/series/GSE13nnn/GSE13041/matrix
and
/geo/series/GSE4nnn/GSE4271/matrix/


The following datasets were not previously analyzed and contained glioma (lower grade) information:

| GEO Reference html | FTP Directory |
| --- | --- |
| https://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?acc=gse43378 | /geo/series/GSE43nnn/GSE43378/matrix/ |
|  |  |

#### 07

```bash
qsub -P reimandlab -l h_vmem=20G,h_rt=0:12:0:0 -pe smp 10 /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/02-12-2020/master_exec.sh

```

Continued polishing and improving pipeline code.

Met with oliver and gained insight into the analysis previously performed (https://docs.google.com/presentation/d/1spQBwQTZzZ_rsR6apgxaJJS4oZGDa13-ttSBRfsoHx0/edit#slide=id.g58d45ac3b4_6_5). 

#### 09

Finished polishing and improving pipeline code. Ran the entire code from start to finish. 

Multiple problems arose from the results analysis script.

#### 14

Re-ran the master scripts after correcting observable problems with the results analysis script. 

```bash
qsub -P reimandlab -l h_vmem=20G,h_rt=0:12:0:0 -pe smp 10 /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/02-12-2020/master_exec.sh
```

Began looking at copy number variants:

__all_data_by_genes_whitelisted.tsv:__ Gene Symbol     Locus ID        Cytoband 

__all_thresholded.by_genes_whitelisted.tsv:__ Gene Symbol     Locus ID        Cytoband 

__broad.mit.edu_PANCAN_Genome_Wide_SNP_6_whitelisted.seg:__ Sample  Chromosome      Start   End     Num_Probes      Segment_Mean

__hms.harvard.edu_PANCAN_IlluminaHiSeq_DNASeqC.cna_whitelisted.seg:__ Sample  Chromosome      Start   End     Segment_Mean

#### 15 

The full master script execution from yesterday failed in certain parts for the paraganglioma cohort. This was not addressed or corrected.

A small naming error was corrected for the results analysis part of the script. 

```bash
qsub -P reimandlab -l h_vmem=30G,h_rt=0:12:0:0 /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/02-12-2020/master_exec.sh
```

#### 16 

Studied and reviewed Proteomics.

#### 17

Studied and review Proteomics, Machine Learning and stats.

#### 18

Transpose a table from the command line.

```bash
awk '
{ 
    for (i=1; i<=NF; i++)  {
        a[NR,i] = $i
    }
}
NF>p { p = NF }
END {    
    for(j=1; j<=p; j++) {
        str=a[1,j]
        for(i=2; i<=NR; i++){
            str=str" "a[i,j];
        }
        print str
    }
}' file
```

## 2021
### January
#### 3

When loading data within functions, the data remains loaded only within the specific function, not within subfunctions or globally.

Solved the problem causing the results not to be written after en analysis. Re-analyzed for all cancer types.

```bash
qsub -P reimandlab -l h_vmem=11G,h_rt=0:12:0:0 -pe smp 10 /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/02-12-2020/master_exec.sh
```

#### 4

Finished correcting the figure-generating script. No errors observed, however, IDH-status has not yet been incorporated into the master pipeline. 

```bash
qsub -P reimandlab -l h_vmem=30G,h_rt=0:12:0:0 /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/02-12-2020/master_exec.sh
```

#### 5

Began incorporating IDH status and gender into the pipeline, along with an easy-to-modify method of considering other clinical factors. Finished correcting the pipeline.

#### 6

Re-ran the whole script from start to finish under the sub-directories titled 06_01_2021 with the following command:

```bash
qsub -P reimandlab -l h_vmem=11G,h_rt=0:12:0:0 -pe smp 13 /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/06_01_2021/master_exec.sh
```

Began analyzing validation dataset ```/.mounts/labs/reimandlab/private/users/abahcheli/ncbi_data/gbm_array_clin_01.02.2013```. The validation data set is from the GeneChip HG-U133 Plus 2.0 Arrays by Affymetrix. Genbank IDs can be collected from the [GEO page containing information on the array](https://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?acc=GPL570). 

#### 7

The gene expression arrays divide the human genome into fractions. There is not necessarily a unique probe for each gene, but instead there is a probe for a region of the genome. Thus, multiple probes may comprise a single gene. 

Started a new directory and master script for ion channels analysis: 07_01_2021.

Worked on standardizing input data from validation datasets.

#### 8

Finished script to standardize validation datasets from GEO database array data. 

Made python script to pre-process tcga data with options for:
- Specify the cancer of interest (from tcga reference file)
- Specify the genes of interest (from hgnc reference file)

Worked on integrating steps for validating expression data into pipeline. 

```bash
qsub -P reimandlab -l h_vmem=10G,h_rt=0:12:0:0 -pe smp 20 /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/06_01_2021/master_exec.sh

qsub -P reimandlab -l h_vmem=20G,h_rt=0:12:0:0 /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/07_01_2021/master_exec.sh
```

#### 9

Validated and corrected python script to pre-process tcga data.

Created script to create KM plots of genes of interest in the validation datasets. 

#### 10

Improved the run time on 000-data_pre_processing.py by avoiding temporary file writing.

The figure-generating script transforms the data prior to predicting outcomes. It then categorizes the predicted outcomes accordingly. Note that the outlier and medianm dichotomization methods are dependent on the distribution of the data being analyzed (different dataset may have different gene expression distributions).

```bash
qsub -P reimandlab -l h_vmem=20G,h_rt=0:12:0:0 /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/07_01_2021/master_exec.sh

qsub -P reimandlab -l h_vmem=10G,h_rt=0:12:0:0 -pe smp 20 /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/07_01_2021/master_exec.sh
```

#### 11

Added a step in the data pre-processing to specify only the first sample from each patient in the tcga cohort.

Created validation figures for glioblastoma_multiforme based on the 01.02.2013 micro-array dataset. 
- 

```bash
qsub -P reimandlab -l h_vmem=10G /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/07_01_2021/master_exec.sh
```

Validated the entire master_exec.sh script.

#### 12

Potential committee member combinations:

Jared Simpson                       Peter Dirks
Faculty of Computer Science         Faculty of Molecular Genetics
OICR                                Peter Gilgan Centre for Resarch and Learning

OR 

Jared Simpson                       Kieran Campbell
Faculty of Computer Science         Faculty of Molecular Genetics
OICR                                Lunenfeld-Tanenbaum Research Institute

#### 14 

Created figures for 01.02.2013 GBM-clin array data. 

#### 15

Re-ran whole script 

```bash
qsub -P reimandlab -l h_vmem=20G,h_rt=0:12:0:0 /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/2021_01_15/master_exec.sh

qsub -P reimandlab -l h_vmem=10G,h_rt=0:12:0:0 -pe smp 20 /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/2021_01_15/master_exec.sh
```

Notice that the -GPLXX indicates the series that is used for the microarray. For those that do not have array information:

Determined which data are for which arrays:
| Dataset | Array |
| --- | --- |
| glioblastoma_multiforme-2013.02.01 | GPL5188-122* |
| glioblastoma_multiforme-2016.01.08 | GPL570* |

This is also written in '/.mounts/labs/reimandlab/private/users/abahcheli/ncbi_data/_arrays_/array_keys.txt'

#### 18

Re-wrote the elastic net and univariate coxph analysis scripts to run all dichotomization analysis at the same time. This significantly improved the run-rime of the scripts.

Starting writing a code to compare all gbm_arrays with clinical samples.

Nanopore reads are sufficient for cancer sampling because sc-sequencing is non-zero inflated. 


#### 20

Worked on master validation script in python.

Note: validation data in '/.mounts/labs/reimandlab/private/users/abahcheli/ncbi_data' has clinical survival information if it is titled by the cancer's full name (eg "glioblastoma_multiforme"), otherwise it does not contain clinical information (eg "gbm").

#### 21

Debugged and completed master validation script. 

Executed validation and generated figures.
```bash
qsub -P reimandlab -l h_vmem=20G,h_rt=0:12:0:0 /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/2021_01_15/master_exec.sh
```

#### 25

Pre-processing for CNA completed (004-cna.py and gene_loci_by_cytoband function).

```bash
qsub -P reimandlab -l h_vmem=40G /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/bin/2021_01_15/master_exec.sh
```

Generated ion channel outlier and fold change for other cancers:
[oO]varian
[lL]ung
[eE]sophageal
[pP]ancreatic
[sS]tomach
[lL]iver
[kK]idney
[bB]reast
[tT]esticular

Began incorporating clinical factors (IDH mutation status, gender, mgmt methylation status). 

#### 27

There was a file with cna for each gene and sample of the TCGA cohort. The sample IDs on this file are not exactly the same as the IDs on the expression data: only the first 4 "-" delimited fields are (eg TCGA-02-2486-01A). 

Finished processing cna data (same order as expression data and survival data).

### February
#### 01

Figures correctly generated for the various types of cancers as described above. 

CNA figure generation began. 

#### 02 

Began mutual exclusivity analysis for ion channel pairs using Fisher's exact test. 

#### 03 

There is no idh mutation status declared in the TCGA dataset:
['kras_mutation_found', 'kras_mutation_result', 'egfr_mutation_performed', 'egfr_mutation_result', 'kras_mutation_codon', 'egfr_mutation_identified', 'ldh1_mutation_found', 'ldh1_mutation_test_method', 'ldh1_mutation_tested', 'genotyping_results_gene_mutation_not_reported_reason', 'mpnst_specific_mutations']

#### 04 

Finished mutual exclusivity analysis for ion channel pairs and additional prognostic factors for both the TCGA dataset and validation datasets.

Began re-training coxph and applying anova to compare fits for additional factors that can be considered as prognostic. These factors included idh_status, mgmt_methylation, gender and age (non-binary).

Method to copy directory over 2 hosts: 
```bash
scp -r -oProxyJump=<EMAIL> <EMAIL>:/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels/results/2021_01_15/figures/ .
```

#### 05

Finished Coxph comparative analysis script (comparing Coxph models with only ion channel expression to those with gender, idh, age, and mgmt methylation status).


#### 08

TCGAbiolinks has some access to more TCGA data.

Look at GTEx for brain expression of ion channels and sex-specific expression of ion channels.

Positive p-value for wilcoxon rank sum in mutual exclusive step (005) means that there is higher expression in: males, idh-mutants and methylated-mgm (greater alternative hypothesis).

Positive p-value for fisher exact in mutual exclusive step (005) means the 'greater' alternative hypothesis is adopted: in those samples, there are more outliers in the first gene (column gene) compared to the second (row gene).

#### 11

Use pyGDC for accessing tcga data in python (https://pypi.org/project/pygdc/). Unfortunately, pyGDC is not conda-installable. Probably best to use TCGAbioconductor instead.

GBM does not have idh_status information, only LGG. 

Changed the ldh_mutation_found in tcga clinical data stored in personal directory to idh_status.

Combined p-values from different datasets using stouffer's method (different datasets do not have interferring p-values).

https://static1.squarespace.com/static/53e7daf0e4b0f490b900867f/t/5e39a56541c9d04afaaacf1b/1580836198681/MoGenStudentHandbook_Feb2020.pdf

#### 12 

Note that Lifelines in conda requires separate installation of formulaic. 

Goal for comparing coxph models is to calculate if there is a difference between predicted survival and actual survival for IC gene-only CoxPH models and IC gene + other clinical factors models.

Used deviance residuals between trained model's prediction (80 of data trained) and actual outcomes as evaluation for ANOVA test. 

#### 15

Finished generating GBM and LGG figures for prognostic factor comparison.

Generated Fisher's test figures and wilcoxon rank sum test figures for expression of ion channels (clustered by covariant expression) and gender, idh_status and mgmt_methylation_status.

#### 16 

Process more lgg (with some gbm) datasets (lacking survival status and times). These datasets were all labelled lgg. Remaining gbm datasets in the NCBI directory are childhood gbm. 
The processed data was put in validation_2 of the working data directory. 

Lgg data from the tcga cohort was used to generate the same figures as the gbm data (except for the coxph model comparisons). 

It was noticed that some of the gbm validation data has lgg data within the datasets. 
The lgg data and gbm data within each dataset were subsequently split (after reprocessing to identify the grade and histology of gliomas).

The final result has lgg and gbm data with clinical outcomes in the 'validation_survival' directory, and datasets without clinical outcomes in the 'validation_no_survival' directory. 

#### 18

Known driver genes from intogen database (https://www.intogen.org/search?cancer=GBM) were used to look at CNA correlation between ion channels and known drivers.
Intogen contains GBM data from:
- TCGA
- PCAWG
- HARTWIG
- ICGC

LGG data from:
- TCGA
- PCAWG
- St. Jude's Children's Research Hospital

Copy Number Alterations between driver genes and ion channels were correlated in TCGA dataset for GBM and LGG. Expression between driver genes and ion channels were also correlated (spearman correlation) between GBM and LGG. 

Fisher's exact test was also applied to expression outliers between drivers and ion channels.

Packages required to be individually downloaded for python env:
- pandas
- lifelines
- formulaic

#### 19

The validation data had been processed to contain all genes, not just ion channels. The all-genes validation data was re-processed to contain a subset of just ion channels in the directory gbm/lgg _validation_ion_channels. 

Began working on GTEx sex-based expression data. Effect size and Local False Sign Rate (LFSR) values for sex-based brain tissues from the following brain loci:
- Brain_Amygdala
- Brain_Anterior_cingulate_cortex_BA24
- Brain_Caudate_basal_ganglia
- Brain_Cerebellar_Hemisphere
- Brain_Cerebellum
- Brain_Cortex
- Brain_Frontal_Cortex_BA9
- Brain_Hippocampus
- Brain_Hypothalamus
- Brain_Nucleus_accumbens_basal_ganglia
- Brain_Putamen_basal_ganglia
- Brain_Spinal_cord_cervical_c.1
- Brain_Substantia_nigra

Use http://useast.ensembl.org/biomart/martview/ (biomart) to generate the HGNC information for each ensembl gene from the human genome.

Correlating sex-based expression effect size with -log wilcoxon rank sum p-value. 

#### 22

Finished generating figures for current progress (LGG and GBM):
- CNA
- Exclusive expression: genes vs genes and genes per clinical
- CoxPH model comparison

LGG tcga data did not have information for idh_status in the clinical directory. This should be collected from TCGA biolinks. 

#### 23

Very few locally downloaded LGG and no GBM samples had idh status information. There appeared to be over 1100 samples for LGG and GBM on TCGA. GBM and LGG data and clinical information was downloaded (refer to tcga_download.r).


### March
#### 02

Gary Bader has ssRNA seq for GBM.

Started pathway enrichment analysis on the driver gene vs. ion channel expression and CNA plots. Using gseapy (Gene Set Enrichment Analysis python interface: https://pypi.org/project/gseapy/), used enrichr API within python with different databases to enrich for specific chromosome(s) and function:

- Chromosome_Location
- GO_Biological_Process_2015
- KEGG_2016

Did not do:
- GO_Molecular_Function_2015
- GO_Cellular_Component_2015

#### 03 

Finished preliminary script for using enrichr to determine enriched chromosomes and pathway from driver and ion channels in CNA data for chromosome_location only.

#### 09

Created a tsv file to interpret the biological explanation of significantly prognostic factors. Started with understanding chromosomal proximity by running CNA correlated events with a minimum spearman correlation rho of 0.8. 

Pathway analysis of differentially expressed genes when each prognostic factor is highly or lowly expressed. Samples were median-dichotomized according to expression of the prognostic factor. For each gene, wilcoxon rank-sum was used to test for a difference in expression between each set of samples. Refer to results.md for more detailed information (cutoff of FDR < 0.05, foldchange > 1.5).

For active pathways, downloaded gene ontology set from http://www.gsea-msigdb.org/gsea/msigdb/collections.jsp: C5: ontology gene sets (browse 14765 gene sets)

#### 11

Finished pathway analysis using both enrichr and Active Pathways. Downloaded cytoscape on the OICR computer and the two dependencies for enrichment maps: 

- EnrichmentMap app of Cytoscape, see menu Apps>App manager or http://apps.cytoscape.org/apps/enrichmentmap
- EhancedGraphics app of Cytoscape, see menu Apps>App manager or http://apps.cytoscape.org/apps/enhancedGraphics

Made a script to build the prognostic factor information tables from the results of the coxph analysis: 008a-prognostic_factor_table.py (requires 008-...R script).

#### 12

Corrected the 006-coxph-master.py script to median-dichotomize data and split into train and test data for the coxph models. 

Collected results from Active Pathways for cytoscape figure generation. Started generating pathway enrichment figures.

#### 19

Knockdown results from GBM cell lines were received from the Xi lab. The results include cell viability assay for GJB2, SCN9A and AQP9. 

#### 22

AQP9 and GJB2 were not identified in the elastic net analysis because they were not considered ion channels (unlike SCN9A, which is defined as an ion channel in HGNC descriptions). GJB2 is a gap junction protein, and AQP9 is an aquaporin. 

Began writing a script to implement elastic net regression on CoxPH survival models in python (001-elastic_net.py).

Brief description and implementation: https://scikit-survival.readthedocs.io/en/latest/api/generated/sksurv.linear_model.CoxnetSurvivalAnalysis.html

Detailed description: https://scikit-survival.readthedocs.io/en/latest/user_guide/coxnet.html

guidetopharmacology site has information on the ion channels we should be targetting in our investigation.

#### 23

Re-wrote the pre-processing steps to include only ion channels that are indicated on the guide to pharmacology. Re-wrote the elastic net and rate of outliers (and fold change of outliers). Basically re-wrote most analysis.

There were 286 ion channels defined in the guide to pharmacology tsv. 

#### 28

Prognostic factors were considered significant if in any of the dichotomization methods (median, outlier or logratio) was greater than 0.4 for GBM and 0.5 for LGG. This method identified GJB2, SCN9A and AQP9 as significant, among others.

#### 29

Made some summary figures from the en analysis.

#### 31 

Generated automatic script for graphing IDH status-dichotomized patient survival. 

### April
#### 05

Started investigating how different transformations affect the prognostic ability of different genes under log-transformation conditions. 

#### 07

Ran iterations of log-transformations to determine which became prognostic. Two conditions were considered: normalization and the type of log transformation. The following were pairs of transformations run in the elastic net model:
1. Normalized; log-transformation
2. Non-normalized; log-transformation
3. Normalized; log-transformation x = (x - mean)
4. Non-normalized; log-transformation x = (x - mean) 
5. Normalized; log-transformation and x = (x - mean) / std
6. Non-normalized; log-transformation and x = (x - mean) / std


The goal is to normalize, not standardize, the expression of values prior to running the elastic net regression model. Note: Normalization typically means rescales the values into a range of [0,1]. Standardization typically means rescales data to have a mean of 0 and a standard deviation of 1 (unit variance). 

#### 09

Began writing script for automatically creating figures via ActivePathways and Cytoscape. Generated figure from Jüri's data (/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/sarscov2_ptm/juri_enriched_genes.tsv).

#### 12

Continued writing automatic figure generating script for python-based elastic net interpretation.

#### 13

Note that there are 7 idh-mutant samples and 94 idh-wt samples in the TCGA-GBM dataset (categorized under the ion_channels clinical set). 

Finished writing automatic figure generating script for elastic net interpretation.

#### 14 

Re-ran the elastic net coxph processing model with the following parameters for log-transformation: normalize and log-transform after adding 1.

Finished re-writing the validation data script (step 003). Note that the short-form for gliomas indicate no available survival data. However, some glioblastoma_multiforme datasets actually contain brain_lower_grade_glioma data.

#### 15 

Created a heat map of Juri's gene probabilities vs. the pathways they are a part of.

For Diogo's files, the following are description:

- rewireProbs4_by_gene_AF_missense_only.tsv
    - Contains allele frequency and pop_max for 318 genes

- rewireProbs4_by_gene_avgAF_missense_only.tsv
    - Contains average allele frequency and pop_max for 318 genes

- rewireProbs4_by_gene_by_pop_AF_tiered_missense_only_0.0001.tsv

"AF", implying allele frequency, actually means the allele frequency multiplied by the mimp probability. The high, the better.

#### 19

Generated outlier and median-dichotomized KM survival curves for TCGA's GBM samples found as significant in non-normalized, log-transformed data. Still have to correct for log-transformed CoxPH-trained data to make KM curves.

'''
normalize: bool, default=False
This parameter is ignored when fit_intercept is set to False. If True, the regressors X will be normalized before regression by subtracting the mean and dividing by the l2-norm. If you wish to standardize, please use StandardScaler before calling fit on an estimator with normalize=False.
'''

#### 21

Re-made the survival curves not by predicting outcomes but by splitting sample survival by median and outlier for expression of each ion channel. 

#### 23

Created Covid project pathway enrichment maps using Diogo's results.

For all genes probability of re-wiring by population showing the maximum result: rewireProbs4_by_gene_maxAF_missense_only.tsv
--not_p_value

For the average between all genes: rewireProbs4_by_gene_avgAF_missense_only.tsv

__Note__ this pathway has 720 interactions with covid19 genes and no interactions within itself (between genes of this pathway).

For specific population probabilities by each involved kinase: rewireProbs4_by_mut_by_pop_missense_only.tsv.

Started generating PPI interactions, beginning with a heat map. PPI interactions between:
- PTM sites vs. sarscov2 proteins
- Kinases vs. sarscov2 proteins

#### 26

Finished validation data figure processing and figure generation.

Started re-doing the rest of the analysis for ion channels. 

#### 27

Started generating summary figures to justify why specific ion channels were investigated. 

#### 28

Finished generating summary figures to justify why specific ion channels were investigated. 

#### 29

Finished generating the same data as previously for the new set of ion channels (4a, 4b, 5, 7, 8, 9). 

#### 30 

Continued workling on generating figures from Diogo's covid19 data.

Why is there a non-survival dataset being added to survival directory.

### May
#### 03

Generated the final figures from the results produced on April 29.

#### 04 

Re-wrote pathway enrichment using DESeq2.

#### 05

The majority of pathways involving ion channels contain more than 100 genes. There is minimal or no enrichment for GO pathways when filtering for less than 100 genes when there is minimal differential expression between groups. Used 10 - 1500 gene cutoff.

#### 06

Started working on the medullo collaboration.

#### 07

Created Diogo's figures. 

Generated pathway enrichment results for the medullo project. 

#### 10

Re-made the survival curves for TCGA data.

#### 11

Re-made the enrichment map for medulloblastoma collaboration.

Started investigating treatment efficacy for high-AQP9 in LGG based on colon cancer paper observations (https://www.nature.com/articles/cddis2017282).

73 / 746 columns of clinical information file contain information for LGG (presumably GBM as well). Columns that are of particular interest include:
- radiation_therapy
- 

#### 12

Added option to combine all samples from different groups (uniquely labelled) to the medullo analysis.

#### 13

Finished making succinct slide decks for ion channels project and medulloblastoma project. The slides are kept locally (server and computer) in a powerpoint and then pushed as pptx and pdf files on google drive.

Re-analyzed medulloblastoma project to combine gene from different groups. 

#### 14

Annotated different combinations and iterations of medulloblastoma pathway enrichment results. 

#### 17

Began analysing conditional differential expression based on IDH status (https://www.biostars.org/p/278684/). Used design matrix and contrast matrix with another covariant

#### 18 

Worked on ACBII assignment (assignment 4). 

#### 19

Began working on Diogo's clustering of gene interactions.

#### 20

Worked on ACBII material and assignment.

#### 21 

Finished clustering of Diogo's SARS genes into groups that have interactions with differentially phosphorylated proteins. 

#### 25

Started addressing the question: why does high AQP9 expression correlate with longer disease-free survival in colon-cancer patients (stage III CRC adjuvant chemotherapy responders) treated with 5-FU (higher expression of AQP9 correlates with increased intracellular 5-FU) in Huang et al. Nature 2017 (https://www.nature.com/articles/cddis2017282). 

Began by looking at overall survival in TCGA dataset:
- CoxPH models of different combinations of Colon cancer patients with log-transformed AQP9 expression alone; recorded concordance and coefficients
- CoxPH models of different combinations of Colon cancer patients with log-transformed AQP9 expression alone, adding stage of colon cancer (one-hot encoded); recorded concordance and coefficients

Repeated for GBM and LGG data:
- CoxPH models of different combinations of Colon cancer patients with log-transformed AQP9 expression alone; recorded concordance and coefficients

Repeated again for other cancer types.

Also looked at progression-free survival in TCGA datasets of LGG and GBM:
- CoxPH models of different combinations of GBM / LGG patients with log-transformed AQP9 expression alone; recorded concordance and coefficients

Only 69 / 290 colon cancer samples in TCGA had days_to_death information.

#### 26

Worked on machine learning course and information.

#### 28

Worked on Covid19 project. Copied 

Re-made figures for Diogo's analysis number 6 and Juri's most recent analysis. 

#### 31

Started writing analysis for ion channels from different TCGA cancer types. To do this, the bin directory was cloned and this project starts with 1. For example, the first script to subset cancer types and setup directories is now '101-tcga_ion_channel_cancer_separation.py'. 

Similarly, another master execution script was created labelled master_exec-1.sh. The original master execution script was relabelled as master_exec-0.sh. 

### June
#### 01

Ran elastic net and foldchange analysis for the different cancer types.

Available data for: 
adrenocortical_carcinoma,bladder_urothelial_carcinoma,brain_lower_grade_glioma,breast_invasive_carcinoma,cervical_squamous_cell_carcinoma_and_endocervical_adenocarcinoma,cholangiocarcinoma,colon_adenocarcinoma,glioblastoma_multiforme,head_and_neck_squamous_cell_carcinoma,kidney_chromophobe,kidney_renal_clear_cell_carcinoma,kidney_renal_papillary_cell_carcinoma,liver_hepatocellular_carcinoma,lung_adenocarcinoma,lung_squamous_cell_carcinoma,mesothelioma,ovarian_serous_cystadenocarcinoma,pancreatic_adenocarcinoma,pheochromocytoma_and_paraganglioma,prostate_adenocarcinoma,rectum_adenocarcinoma,sarcoma,skin_cutaneous_melanoma,stomach_adenocarcinoma,testicular_germ_cell_tumors,thymoma,thyroid_carcinoma,uterine_carcinosarcoma,uterine_corpus_endometrial_carcinoma,uveal_melanoma

#### 02

The elastic net analysis was taking too long. Split into different groups for running as different HPC jobs:

adrenocortical_carcinoma,bladder_urothelial_carcinoma,brain_lower_grade_glioma,breast_invasive_carcinoma,cervical_squamous_cell_carcinoma_and_endocervical_adenocarcinoma,cholangiocarcinoma,colon_adenocarcinoma,glioblastoma_multiforme,head_and_neck_squamous_cell_carcinoma,

- [9:12] kidney_chromophobe,kidney_renal_clear_cell_carcinoma,kidney_renal_papillary_cell_carcinoma,
- [12:14] liver_hepatocellular_carcinoma,lung_adenocarcinoma,
- [14:17] lung_squamous_cell_carcinoma,mesothelioma,ovarian_serous_cystadenocarcinoma,
- [17:21] pancreatic_adenocarcinoma,pheochromocytoma_and_paraganglioma,prostate_adenocarcinoma,rectum_adenocarcinoma,
- [21:24] sarcoma,skin_cutaneous_melanoma,stomach_adenocarcinoma,
- [24:27] testicular_germ_cell_tumors,thymoma,thyroid_carcinoma,
- [27:] uterine_carcinosarcoma,uterine_corpus_endometrial_carcinoma,uveal_melanoma


SARS-CoV-2 project: To identify subnetworks within the cluster, tried using the highly-connected subgraphs (HCS) algorithm (https://www.sciencedirect.com/science/article/abs/pii/S0020019000001423?via%3Dihub). 

Algorithm adapted from: https://github.com/53RT/Highly-Connected-Subgraphs-Clustering-HCS/blob/master/hcs.py. For descriptions on HSC algorithms, refer to: https://angom.myweb.cs.uwindsor.ca/teaching/cs592/592-ST-NSB-Clustering.pdf.


#### 03

Worked on computational biology class work.

#### 04 

Improved figures for Covid19 PTMs. Unfrotunately, the highly connected graph clustering algorithm did not work because the dataset only contains information on the Covid19-human interactions and not the human-human interactions.

Cannot use HotNet2 because the Covid19 proteins do not have independent scores for heat dissipation.

#### 07

Worked on computational biology class work.

Began writing scripts for medulloblastoma project that generates heat maps for each of the network clusters and coloured dots for p-values and fold-change of proteins associated with these clusters. 

#### 08

Wrote elastic net interpretation and figure generation script to summarize the multi-cancer elastic net investigation. The script generates a single figure with all ion channel outlier and fold-change results and interprets the elastic net multi-factorial investigation as was previously done with the brain cancers. 

The elastic net analysis was taking too long. Split into different groups for running as different HPC jobs:

- [:3] adrenocortical_carcinoma,bladder_urothelial_carcinoma,brain_lower_grade_glioma
- [3:6] breast_invasive_carcinoma,cervical_squamous_cell_carcinoma_and_endocervical_adenocarcinoma,cholangiocarcinoma
- [6:9] colon_adenocarcinoma,glioblastoma_multiforme,head_and_neck_squamous_cell_carcinoma
- [9:12] kidney_chromophobe,kidney_renal_clear_cell_carcinoma,kidney_renal_papillary_cell_carcinoma
- [12:14] liver_hepatocellular_carcinoma,lung_adenocarcinoma
- [14:17] lung_squamous_cell_carcinoma,mesothelioma,ovarian_serous_cystadenocarcinoma
- [17:21] pancreatic_adenocarcinoma,pheochromocytoma_and_paraganglioma,prostate_adenocarcinoma,rectum_adenocarcinoma
- [21:24] sarcoma,skin_cutaneous_melanoma,stomach_adenocarcinoma
- [24:27] testicular_germ_cell_tumors,thymoma,thyroid_carcinoma
- [27:] uterine_carcinosarcoma,uterine_corpus_endometrial_carcinoma,uveal_melanoma

There was insufficient data for testicular_germ_cell_tumors and pheochromocytoma_and_paraganglioma. They were subsequently removed from the analysis list.

All data available for: 
adrenocortical_carcinoma,bladder_urothelial_carcinoma,brain_lower_grade_glioma,breast_invasive_carcinoma,cervical_squamous_cell_carcinoma_and_endocervical_adenocarcinoma,cholangiocarcinoma,colon_adenocarcinoma,glioblastoma_multiforme,head_and_neck_squamous_cell_carcinoma,kidney_chromophobe,kidney_renal_clear_cell_carcinoma,kidney_renal_papillary_cell_carcinoma,liver_hepatocellular_carcinoma,lung_adenocarcinoma,lung_squamous_cell_carcinoma,mesothelioma,ovarian_serous_cystadenocarcinoma,pancreatic_adenocarcinoma,prostate_adenocarcinoma,rectum_adenocarcinoma,sarcoma,skin_cutaneous_melanoma,stomach_adenocarcinoma,thymoma,thyroid_carcinoma,uterine_carcinosarcoma,uterine_corpus_endometrial_carcinoma,uveal_melanoma

#### 09

Created individual boxplots displaying curve separability by log ratio test and unifactorial CoxPH coefficients for each cancer.

#### 10

Worked on final assignment for ACBII.

#### 11

Worked on final assignment for ACBII.

#### 14

Re-created cancer vs. ion channel heatmaps for rate of EN identification and univariate CoxPH coefficient.

#### 15 

Created KM survival curves for cancers after dichotomizing by prognostic ion channel expression. 

Created heatmaps for each node of the medulloblastoma project's enriched pathways.

Re-ran the pre-processing and elastic net analysis for all cancers to include only a single (the first) sample for each pathway, which increases the frequencies of identifying prognostic ion channels.

Clustered cancer types in PCA and UMAP using expression of our ion channels.

#### 16

Made a simple example on how having more than one sample per patient can significantly influence the prognostic abilities of ion channels (script 902).


#### 17

Created a faster execution script for multi-threading elastic net analysis that uses snakemake: master_elasticnet.smk

For snakemake tutorials and references, refer to:
- https://eriqande.github.io/eca-bioinf-handbook/managing-workflows-with-snakemake.html
- https://snakemake.readthedocs.io/en/stable/executing/cluster.html


I had to make the output files not available: for name in `ls`; do rm $name/*$name*logtransform_en.tsv;  done

Created a snakemake execution script called snake_master in home directory.

snakemake --cluster 'qsub -P reimandlab -l h_vmem={resources.individual_core_memory},h_rt={resources.runtime} -pe smp {resources.threads}' -j 56


Worked on the Covid19 project. Tried removing single gene-network interactions before clustering. Created a new network with the kinases and their substrates identified as significant in Diogo's analysis: /.mounts/labs/reimandlab/private/users/diogopell/COVID/rewireProbs6_all_logos_top_probs.tsv

Created interaction network between kinases and rewired ptm target proteins with 2 kinds of edges:
- Gain of motif
- Loss of motif

#### 18

Re-clustered the covid protein vs. ptm network using markov clustering 004-clustering_of_pni.py. The network had 267 nodes and 706 edges, and clustered into 6 groups. 3 groups contained single genes: N, nsp10 and nsp10ab.

Created new figures for the medullo project as per Juri's recommendations. Used the COSMIC database (https://cancer.sanger.ac.uk/census) for known cancer driver gens (~750 total, 8 medulloblastoma). 

#### 21

Improved the medullo figures according to Juri's recommendations. 

For heatmaps, use the pheatmap function: https://davetang.org/muse/2018/05/15/making-a-heatmap-in-r-with-the-pheatmap-package/

Using snakemake on the cluster: https://snakemake.readthedocs.io/en/stable/project_info/faq.html

#### 22

Created barplot for outlier rank and foldchange by cancer (single figure for outliers).

#### 23

Created new heatmap for elastic net summary using Pheatmap. 

Combined families and created heatmaps for rate of identification in Elastic net and CoxPH univariate coefficient.

#### 24

Worked on machine learning presentation with neural networks, matrix factorization, etc. 

#### 25

Determined ranks of fold change and rate of outliers for each family of ion channel for each cancer. This was done by determining the relative outlier rate and relative fold change compared with 10,000 random combinations of genes of the same size as each family.

#### 28

For the medullo collaboration I changed to p-value for ordering genes across the x-axis. 

Lab meeting notes for medullo project:
- Don't take molecular function or cellular component. Take biological process and reactome pathways only. Keep limiting between 10 - 1000.

- Gary Bader maintains gene sets. Can also go to G:profiler instead of MSigDB. 


Ion channel project:
- Barplot rank ion channels from left to right. Stacked bar plot for different cancer types

- One big figure to say: we have lots of data


#### 29

Medullo project: re-ran the pathway enrichment and re-created pathway figures.

Created a volcano plot of all overlapping genes by the significance of Active Pathways:
- p-value from the FDR of Active Pathways 
- fold change from the original data
- colour known medullo and cancer genes

#### 30

Individual genes top ~100 in the same dotplot

Remove 0s for significantly identified.


### July
#### 05

Created presentation for Journal Club.

Corrected the pathway heat map script for the medullo project.

#### 06 

Created a volcano plot for Medullo project genes that were identified as significantly enriched in certain pathways for each subtype. 

*Reminder for pushing large commits:* large commits at oicr must be pushed using the https command: eg. git push https://github.com/reimandlab/medullo_collab_m_taylor_lab.git

ggplot legend fill issues resolved with: https://aosmith.rbind.io/2020/07/09/ggplot2-override-aes/

Worked on the COVID PTM project by making heat maps for genes vs. pathways from Juri's data and protein-protein interactions. 


#### 07

Created stacked bar plots as part of ion channels 103 script to look at the contributions from each cancer for the top 25 ion channel genes.

Remade medullo pathway enrichment maps with new gmt set. Annotated resulting figures and sent to Juri (Group 3 / 4 with volcano plot). 

#### 08

Update medullo pathways enrichment maps according to Juri's recommendations.

Reviewed literature in preparation to write the introduction of the COVID PTM paper.

#### 09

Create human PPI for ptms vs. ptms and ptms vs. kinases. Used the Biogrid database with phyiscal, multi-validated interactions (https://downloads.thebiogrid.org/BioGRID/Release-Archive/BIOGRID-4.4.199/). File downloaded: BIOGRID-MV-Physical-4.4.199.mitab.zip

TCGA data types (FPKM-UQ) descriptions are available here: https://docs.gdc.cancer.gov/Data/Bioinformatics_Pipelines/Expression_mRNA_Pipeline/

Reminder that druggable gene can be identified in guide to pharmacology: https://www.guidetopharmacology.org/GRAC/ReceptorFamiliesForward?type=IC


#### 12

Colour palette for TCGA information: https://github.com/ICGC-TCGA-PanCancer/pcawg-colour-palette/blob/master/pcawg.colour.palette.R

All kinases for PPI network is here: https://www.uniprot.org/docs/pkinfam

#### 13

Total number of interactions between PTM proteins: 897

Total number of interactions between PTM proteins and kinases: 896

Total number of interactions between PTM proteins and covid proteins: 332

Create PPI network visuals. 

Kept working on the introduction to the Covid ptm paper.

#### 14

Created new PPI networks from the newest data with a cut-off of fdr < 0.05: /.mounts/labs/reimandlab/private/users/jreimand/COVID_PTM/data/110721/gene_scores_list__3POP__COVID.rsav

Total number of interactions between PTM proteins (47 genes): 3
Total number of interactions between PTM proteins and kinases (526 genes): 33
Total number of interactions between PTM proteins and covid proteins (127 genes): 9

Without the cut-off of fdr < 0.05:
Total number of interactions between PTM proteins (743 genes): 72
Total number of interactions between PTM proteins and kinases (526 genes): 820
Total number of interactions between PTM proteins and covid proteins (127 genes): 313

Corrected colours for cancer type plotting in dimensional reduction processes (umap, tsne, pca).

#### 15

Combining all genes for interactions derives 4207 interactions. 

Ptms vs ptms: 
Original number of interactions: 23
Total number of interactions: 3

Ptms vs. kinases:
Original number of interactions: 39
Total number of interactions: 13

Ptms vs. covid19_genes:
Original number of interactions: 17
Total number of interactions: 9

Ptms vs. both covid genes and kinases:
Number of TopGenes genes: 47
Number of kinases: 526
Number of covid19 proteins: 127
Total number of interactions between TopGenes (ptms) and kinases or covid19 genes: 77

#### 19

Finished first draft of covid19 PTM introduction.

#### 20 

Ran permutation test on ppi for all ptm-containing genes to identify whether we are seeing more interactions with our proteins than expected by chance. 


2 independent pieces of literature were not required. PPIs identified in BIOGRID-ORGANISM-Homo_sapiens-4.4.199.mitab.txt






TopGenes ptms vs. TopGenes
Original number of interactions: 34
Total number of non-self interactions: 17

TopGenes ptms vs. Kinases
Original number of interactions: 391
Total number of non-self interactions: 307

TopGenes ptms vs. Covid19 genes
Original number of interactions: 48
Total number of interactions: 34

TopGenes vs. Kinases and Covid19 genes 
Number of genes in first: 47
Number of genes in other group: 526
Number of genes in other group: 52

Total number of interactions: 358

Average edges per node (all nodes): 1.7577319587628866
Note that the number of edges per node averaged across all nodes is less than the average for any specific group because there is one edge shared between any two nodes.

Average edges per TopGenes ptm node: 8.136363636363637
Number of interactions between TopGenes ptm and TopGenes ptm: 17
Number of nodes for TopGenes ptm: 44
Number of edges for TopGenes ptm: 341

Average edges per node for kinases: 2.3435114503816794
Number of interactions between TopGenes ptm and kinases: 307
Number of nodes for kinases: 131
Number of edges for kinases: 307

Average edges per node for Covid19 genes: 1.7894736842105263
Number of interactions between TopGenes ptm and Covid19 genes: 34
Number of nodes for Covid19 genes: 19
Number of edges for Covid19 genes: 34


Permutation tests for all ptm-containing genes to identify whether we are seeing more interactions with our proteins than expected by chance. 


Number of genes with ptms: 14175
Number of Covid19 genes: 52
Number of interactions: 34
p-value (pdf) of ptm TopGenes interaction rate: 0.806

Number of genes in with ptms: 14175
Number of kinases: 526
Number of interactions: 307
p-value (pdf) of ptm TopGenes interaction rate: 0.006


#### 21

Finished manually functional annotations for the list of TopGenes (ptms). 

#### 22

Created presentation for OICR's monthly Monday Informatics Lunch.

#### 23

Finished presentation.

#### 26

Re-made PPI figure with ActiveDriver Site-specific network of kinases and targets* for TopGenes - kinases interactions.

Average edges per node: 1.1428571428571428

Average edges per node for organism 0: 3.0588235294117645
Number of interactions between organism 0 and organism 0: 17
Number of nodes for organism 0: 34
Number of edges for organism 0: 104

Average edges per node for Kinases: 1.394736842105263
Number of interactions between TopGenes ptms and Kinases: 53
Number of nodes for Kinases: 38
Number of edges for Kinases: 53

Number of genes in first array: 14175
Number of genes in other array: 526
p-value (pdf) of ptm TopGenes: 0.046

Average edges per node for SARS-CoV2 Genes: 1.7894736842105263
Number of interactions between TopGenes ptms and SARS-CoV2 Genes: 34
Number of nodes for SARS-CoV2 Genes: 19
Number of edges for SARS-CoV2 Genes: 34
p-value (pdf) of ptm TopGenes: 0.006

#### 27

Created a new PPI figure with information for Kinases from the ActiveDriver DB and information for TopGenes from BIOGRID-ORGANISM-Homo_sapiens-4.4.199.mitab.txt.

#### 28

Started iteratively going through all possible TopGenes with and without PPI to identify genes with possible disease phenotypes. 

#### 29

Started writing script to determine how informative clinical information and histological diagnosis is for predicting outcome and dichotomizing patients. 

Considered the following factors:
- gender
- age_at_initial_pathologic_diagnosis
- race
- ethnicity
- pathologic_stage
- nuclear_grade_iii_iv


### August
#### 09

Look through https://www.ncbi.nlm.nih.gov/snp for SNPs associated with different genes. 

#### 10

Created km survival curves for each individual prognostic factor. 

#### 11

Finished annotating new gene set for Covid19 PTM project. Started writing Snakemake tutorial for the cluster.

#### 12 

Delete all snakemake jobs:
qstat | grep snake | awk '{print $1}' | xargs qdel

Apparently, snakemake has an issue with coninuing to run jobs in SLURM (https://github.com/snakemake/snakemake/issues/759).

#### 23

Reanalyzed Juri's data that was put into the most recent presentation. Used combined_gene_scores for pathway analysis.

Created powerpoint for Medulloblastoma collaboration. 

#### 26

Created figure legend for covid-ptm PPI and enrichment map.

Updated and corrected powerpoint for medulloblastoma collaboration.

#### 31

Wrote analysis for Figure 2 of COVID-PTM paper.

Re-made Figure 1A explanation for COVID-PTM paper.

Completed data analysis for Ion Channels clinical factor contribution.


### September
#### 01

Re-made the Figure 1a explanation (overview of COVID-PTM methods).

#### 02

Made a new folder for the medulloblastoma collaboration and updated the code. Created a new combined figure with all subtypes combined.

#### 07

Created a final figure for the Taylor lab (medulloblstoma collaboration).

Created an explanatory figure (Figure 1a explanation) for the Covid-ptm project.

Wrote a rough draft of the results section for Figure 3. Remember, kinase top genes interactions (kinase-protein interactions by kianse motif) are from the ActiveDriver DB (https://activedriverdb.org).

#### 08

Complete and sent a rough draft of the results section for Figure 3 (mostly completed yesterday).

Completed ion channels figure for different clinical factors' prognostic abilities (clinical_cph_contributions.pdf), using an upset plot (https://github.com/const-ae/ggupset).

#### 09

Modified 103b-km_clinical_survival_curves.R to create a stacked barplot of the Wald test p-values and likelihood ratio test p-values, prioritizing clinical factors for their ability to discriminates between survival. 

#### 10

Modified 103b-km_clinical_survival_curves.R again to create individual barplots of the Wald test p-values and likelihood ratio test p-values, prioritizing clinical factors for their ability to discriminates between survival. 

#### 12

Created journal club presentation for tomorrow.

For medulloblastoma collaboration, added auto-annotated (word-cloud) for pathways in cytoscape.

#### 13

Finished the medulloblastoma pipeline and pushed to github.

#### 14

Started writing scholarship applications for OICR's CGS-D application.

Ran nanoplot on the nanopore sequencing data:

nohup NanoPlot -t 10 -f pdf -o /.mounts/labs/reimandlab/private/users/abahcheli/nanopore_tmp/ --loglength --summary sequencing_summary.txt &

#### 15

Reviewed Diogo's writing for the Covid-PTM paper.

#### 20

Created a presentation for lab meeting.

#### 21

Started incorporating recommendations from the Monday presentation. 

Link for downloading gene sets from g:Profiler: https://biit.cs.ut.ee/gprofiler/static/gprofiler_hsapiens.name.zip.

#### 27

Created a new version of ion channels analysis: 2021_09_27. Copied the processed data from the last version, started re-running elastic net and fold change analysis in snakemake.

Run snakemake for fold-change family members. 

#### 28

Re-created the medulloblastoma collaboration figure and methods section with a log2-foldchange cutoff of 0 (had to have a fold change of at least 1).

#### 29

To remember: if a tumor sample has >90,000 mutations, exclude it from analysis (approximately 30 mutations per megabase (Mb)).

Continued modifying figures according to lab recommendations: 37099.28 10 threads; 10070.47 28 threads.

#### 30

MMG1011:
- Keep it simple, do not over-whelm the audience
    - Shorter and simple is better

Started re-writing the clinical factor prognostic analysis to:
1. Determine how clinical factors can influence the prognostic-ability of CoxPH models using an ANOVA. 
2. Combined all sub-stages of cancer into the major stages classified as 1-4. 

Acquired the GBM Illumina data but the data was very, very small (less than 1Mb each).

### October
#### 01

Changed CoxPH coefficients in figures to Hazard Ratio. 

Pseudo-alignment algorithm for RNA-seq / exome alignment to human genome: Kallisto.

#### 04

Removed race and ethnicity from 006-multifactorial clinical factors. Combined all sub-subtypes into the major subtype (eg iia, iib now becomes ii).

https://stats.stackexchange.com/questions/119790/difference-in-chi-squared-calculated-by-anova-from-cph-and-coxph/138932

#### 05

Removed nuclear grade from clinical prognostic factors.

Worked on improving figures for ion channels project. Major changes included:
- Coloured dotplots according to the background cancer TCGA colour (some labels were black text, others were white text)
- Added all ion channels identified frequency to plots and greyed out ones that were not significant

#### 06

Created a network view of significant interactions between ion channels and driver genes.

CNA: discretize data: 0 vs. above a threshold 
    - Do CNA amplifications first


#### 12

Finished generating survival curves for TCGA gliomas: prognostic ion channels controlled for idh status combined with boxplots.

Processed glioblastoma validation data.

#### 13

Some CNA from the raw data are less than 0 (often to -0.4). Re-ran expression and CNA correlations between known drivers and ion channels using raw expression data dichotomized into 3 groups: <0, between 0 and 0.3, >0.3


Finished analyzing validation data for survival outcomes and curve separation.

#### 14

Wrote up summary on TBK1 for the covid-19 ptm paper. 

Corrected some of the recommendations on the ion channels project presentation (corrected scale for CoxPH hazard ratio, increased font of figures). 
The major concern is targetting ion channels in the brain. 

#### 15

Finished implementing figure changes from presentation recommendations.

Started implementing recommendations in the CGS-D application.

#### 18

Added a "reverse" analysis to the clinical validation steps (006) for ion channels project: the ion channels as additional prognostic markers were compared to the ability of clinical factors to predict survival outcome:
- H0 = (survival ~ clincal_outcome)
- H1 = (survival ~ clincal_outcome + ion_channels_expression)

This did nothing different from the original analysis because ANOVA is not directional.

Started doing conditional correlation between genes accounting for IDH status. Basically, ran a chi-squared test on the residuals to test whether they were significantly reduced or not.

#### 19

Found a better package for doing linear model conditional correlation between genes accounting for IDH status.

Good package for R-like linear model building in python: statsmodels
- from statsmodels.formula.api import ols
- from statsmodels.stats.anova import anova_lm
- model = ols("y ~ x", data).fit()
- anova_results = anova_lm(model)

#### 20

Created tile plot of SCN9A mutations vs. IDH1 mutations with gender and idh_status as cofactors for the ion channels project.

#### 21

GBM WGS Illumina sequencing data is in the following location: /.mounts/labs/reimandlab/production/:
210422_A00469_0171_BH2C5HDSX2.GBM.fastqs
210427_A00469_0172_AH2VHKDSX2.GBM.fastqs
210513_A00469_0176_AHWJYWDMXX.GBM.fastqs
210601_A00469_0179_BHCKFVDRXY.GBM.fastqs
210601_A00469_0180_AH77VJDSX2.GBM.fastqs

These were copied to /.mounts/labs/reimandlab/private/generated_raw_data/GBM_primary_recurrent_genomic/illumina_shortreads/

Table for barcode decoding: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/data/ref_data/patient_barcodes.tsv.

Use Biorender for generating nice figures / visuals / cartoons.

#### 22

Reviewed v16 of the Covid-ptm paper. 

Created presentation for lab meeting on Monday.

#### 25

Finished writing the committee meeting report.

#### 26

Clustering for heatmap.2 / heatmap2: https://www.datanovia.com/en/blog/clustering-using-correlation-as-distance-measures-in-r/

#### 27

Ion channels project: created chromosome-segment visualizations.

HG38 gene loci:
IDH1 is Chr2, 208255246 - 208256181
SCN9A is Chr2, 166195184 - 166376001

To obtain all gene bed files: https://bioinformatics.stackexchange.com/questions/895/how-to-obtain-bed-file-with-coordinates-of-all-genes

R genomic ranges (GenomicRanges / genomicRanges): https://bioconductor.org/packages/release/bioc/vignettes/GenomicRanges/inst/doc/GenomicRangesIntroduction.html

R gviz: https://www.bioconductor.org/packages/devel/bioc/vignettes/Gviz/inst/doc/Gviz.html

Downloading TCGA data: https://xenabrowser.net/datapages/?cohort=TCGA%20Pan-Cancer%20(PANCAN)&removeHub=https%3A%2F%2Fxena.treehouse.gi.ucsc.edu%3A443

#### 28

Prepared committee meeting presentation for meeting with Sunit Das tomorrow.

Revised Covid PTM introduction. 


#### 29

The CNA association between GJB2 and GJB6 is likely due to proximity (both are at NC_000013.11).
GJB2: 20187470 - 20192938 (complement)
GJB6: 20221962 - 20232319 (complement)


### November
#### 01

Presented in committee meeting. 

Started re-writing the elastic net method according to the lnc-RNA paper (with feature pre-selection).

#### 02

Added clinical features to the clustering plot.

Finished re-writing 2 version of the elastic net script:
- Feature pre-selection of ion channel genes with minimum hazard ratio difference of +-0.2
- Feature pre-selection of ion channel genes with minimum hazard ratio difference of +-0.2 and clinical factors included

#### 03

Continued working on debugging the ion channel feature pre-selection and visualization.

#### 04

Finished debugging the feature pre-selection. Created new summary figure for rate of ion channel identification.

Started creating a new version of the pipeline with cleaner functions and succinct scripts.

#### 08

Started implementing recommendations from Sunit Das for improved figures and investigations: 
- Figure 1 barplot limited to ion channels of categories: lgic (ligand-gated ion channel), vgic (voltage-gated ion channel), other_ic (other ion channels, including facilitated diffusion ion channels such as Aquaporins)

#### 09

FDR correction explained: https://stats.stackexchange.com/questions/238458/whats-the-formula-for-the-benjamini-hochberg-adjusted-p-value

Worked on CGS-D application.

#### 10

Worked on figure for weekly presentation: frequency of feature-preselection for ion channels with CoxPH EN frequency > 0.15 (feature passed pre-selection if Wald p < 0.1).

Worked on developmental neurobiology course.

#### 14

Finished OGS-D application.

Worked on developmental neurobiology course.

#### 18

Created a new repository for ion channel investigations in the reimandlab organization: ion_channels_gbm.

#### 22

Started incorporating Schoenfeld residuals into my Elastic Net analysis.

#### 23

Prepared for the developmental neurobiology course.

#### 24

Downloaded GRCh38 fasta for RNA-seq genes.

Installed Kallisto on wgs_nanopore conda environment (http://pachterlab.github.io/kallisto/manual.html). Used it to count mRNA transcripts of our GBM validation cell lines. Combined all cell line counts into a single dataframe. 

#### 25

Finished ActivePathways enrichment of cell lines and TCGA data for differential expression of genes in ion channel KO and high-vs-low expression samples.

#### 29

Visualized cell line and TCGA pathway enrichment in Cytoscape.

Values:
AQP9: #9aeaf4
GJB2: #d6b153
SCN9A: #b5eab5
All: #ffffff


Wrote up rough methods section (point form) for ion channels project.

#### 30

Re-did the pathway enrichment: combined all cell lines and all tcga separately. 

### December
#### 01

Re-wrote the fold change and ion channel analysis script (001).

Moved the ion channels manuscript and google slides to a microsoft word document and keynote slides, respectively. These documents are stored on OICR's OneDrive and shared with Juri. 

#### 02

Started snakemake for fold change and elastic net 13964.

#### 06

Ivy Glioblastoma Atlas Project (https://glioblastoma.alleninstitute.org) contains RNA-Seq data, imaging data (ISH), specimen Metadata.

Started looking into TCGA matched normal / GTEx data for expression of ion channels under normal conditions (https://ucsc-xena.gitbook.io/project/how-do-i/tumor-vs-normal).

Normalized (TPM) counts were downloaded from GTEx data (https://gtexportal.org/home/<USER>

#### 07

ugehn02

jobid: 14528

#### 08

Created new outlier fold change plot, ion channel fold change for GBM, and other (presented in lab meeting).

#### 09

Worked on improving figures according to Jüri's recommendation for the presentation tomorrow.

#### 10

Checkout oncoprint for visualizations: https://jokergoo.github.io/ComplexHeatmap-reference/book/oncoprint.html


#### 13

Investigated expression correlations in our top genes and known drivers.

Strated re-writing code to a more distinct version.

#### 14

Tested Snakemake for the ability to run snakemake on a compute node. It seems to be working.

Continued re-writing scripts.

## 2022
### January
#### 01

New ion channels script version created.

#### 07

Finished re-writing codes for succinctness. 

#### 10

Started creating new figure visualizations for the elastic net results.

3038 samples from TCGA data on the cluster have survival information.

#### 12

168 unique ion channels were identified in the elastic net method (frequency > 0.5). Overall, 168 / 271 = 62% of ion channels were considered prognostic across 28 cancer types.

#### 14

No acute_myeloid_leukemia samples have matching clinical data (filtered out). No chronic_myelogenous_leukemia samples exist in the TCGA data.

Overall, 3052 out of 10117 samples have informative survival data. 9869 samples have paired clinical data.

#### 17 

Started working on the Covid PTM revisions:
- Identified many databases with WGS / WES data.
- Defined neutral vs. positive selection and started identifying the differences in our arguments.
- Started working on the revisions to the pathway enrichment figure colours.

#### 18

New version started: 2022_01_18.

#### 19

Canadian cancer society survival statistics (https://cancer.ca/en/cancer-information/cancer-types/brain-and-spinal-cord/prognosis-and-survival/survival-statistics).

#### 20

SARS-CoV-2 PTM analysis: pmid 29459708 used HG19. 

Human genome 19 gene loci were downloaded from here: https://www.gencodegenes.org/human/release_19.html

#### 24

139 / 2033 sites are enriched in the 

#### 25

Masoom's progress:

- Looked at correlations between expression leves and clinical factors (Rank-Sum test where binomial variable was set)

- Created random forest models to predict expression based on clinical factors
    - Age seemed to be the most important factor

#### 26

Worked on figures for presentations

### February
#### 02

The last few days have been spent working on the presentation and OGS scholarship application, both of which were due February 1.

Started analyzing scRNA-seq data from GSE131928 (https://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?acc=GSE131928). 

Two types of scRNA-seq data:
- Smartseq ranksum test:
    - immune vs. non-immune
        - SCN9A - 0.06101
        - GJB2 - 0.4463
        - AQP9 - 5.952 e-05
    - immune vs. other
        - SCN9A - 0.0335
        - GJB2 - 0.2011
        - AQP9 - 0.2724
    - other vs. non-immune
        - SCN9A - 0.06020
        - GJB2 - 3.005 e-05
        - AQP9 - 9.684 e-10
- 10x ranksum test:
    - immune vs. non-immune
        - SCN9A - 
        - GJB2 - 
        - AQP9 - 
    - immune vs. other
        - SCN9A - 
        - GJB2 - 
        - AQP9 - 
    - other vs. non-immune
        - SCN9A - 
        - GJB2 - 
        - AQP9 - 

#### 04

Continued improving the ion channel results based on recommendations in last week's meeting.

IDH wt vs. mut (alt = two-sided):
- SCN9A: 1.311 e-16
- IDH1: 0.0003624

IDH:
- amp vs. norm (alt = greater): 0.001339
- norm vs. del (alt = greater): 0.2045

SCN9A:
- amp vs. norm (alt = greater): 0.007708
- norm vs. del (alt = greater): 0.002268

#### 05

Re-ran the elastic net analysis replacing non-existent days_to_death values with days_to_last_followup.

#### 06

Re-created figures summarizing elastic net results.

#### 14

Information on TCGA patient survival is provided in TCGA-CDR-SupplementalTableS1 at https://gdc.cancer.gov/about-data/publications/pancanatlas.

This includes: OS, DFI, PFI with various definitions. For example, OS is defined as: OS.time: overall survival time in days, last_contact_days_to or death_days_to, whichever is larger.
Created new survival curves in gliomas for PFI and OS.

Re-wrote the fold-change investigation code to improve processing.

#### 15

Created volcano plots for DEGs from the cell line RNA-seq data.

#### 18 

Created gene-wise contributions to enriched pathways. 

#### 23

Re-made the pathway enrichment plots without a FC cutoff.

#### 28

Created connected line plot for understanding the concordance of CoxPH models with just clinical factors vs clincal factors and ion channels.

### March
#### 03

Re-analyzed GBM and LGG with elastic net adding the following clinical factors: age, sex, idh_status.

#### 07

Downloaded the human genome reference transcript fasta file from: https://www.gencodegenes.org/human/

Downloaded the ensembl translation for the human henome (HG38 / HG39) from biomart: http://useast.ensembl.org/biomart/


Kissy:
- SNV and CNA vs. not CNA
- 

#### 08

RNA seq countins follows this pipeline: https://wikis.utexas.edu/display/bioiteam/RNA-Seq+Analysis+Pipeline

Compared to the output from Juri's pipeline: https://combine-australia.github.io/RNAseq-R/07-rnaseq-day2.html

#### 15

Created a powerpoint summarizing IDH and survival and sent it to Sunit Das. Meeting tomorrow (16 Mar 2022).

#### 16

Meeting with Sunit Das on IDH1 expression prognosis of IDH-mut LGG in TCGA.

Human genome download from UCSC: https://hgdownload.soe.ucsc.edu/downloads.html#human

v3 of mapping reads from Xi lab rna-seq is using Rsubread.

#### 17

Good web tool for visualizing multiple primer alignments to reference sequence: https://www.bioinformatics.org/sms2/primer_map.html


Information on SCN9A transcripts: https://useast.ensembl.org/Homo_sapiens/Transcript/Summary?db=core;g=ENSG00000169432;r=2:*********-*********;t=ENST00000454569

#### 18

Bioinformatics pipeline used: https://wikis.utexas.edu/display/bioiteam/RNA-Seq+Analysis+Pipeline

#### 21

Used the canonical isoform (ensembl canonical trancsript from biomart using filter) for RNA seq data anaylsis.

Re-ran elastic net using additional clinical information as co-factors:
- Age
- Sex
- Tumor Grade

#### 23

Created new visualization for elastic net results:
- Barplots of unifactorial hazard ratios with confidence intervals
- Error bars are 95% confidence intervals

#### 24

Remace the elastic net results figure with the following recommendations by Juri:
- Color nodes by cancer type
- HR on X axis
- expression log2 fold-change in the other axis (high risk group relative to low risk group)
- label all instances of SCN9a, GJB2
- node size could be -log10 p-value
- could add error bars for HR, maybe later

To force snakemake to execute a rule, add -R:
```sh
/.mounts/labs/reimandlab/private/users/abahcheli/anaconda3/envs/py_dev/bin/snakemake -R elastic_net --cluster "/opt/uge-8.6.18/bin/lx-amd64/qsub -P reimandlab -l h_vmem={resources.individual_core_memory},h_rt={resources.runtime} -pe smp {resources.threads}" -j 30
```


#### 25

Created rough presentation for committee meeting (pre-qualification). 

Wrote rough outline of my qualification proposal.

#### 28

To run snakemake with conda environment (snakemake conda), use "qsub -V..." to pass environment variables, including the current conda environment (https://github.com/snakemake/snakemake/issues/883).

Re-ran the entire RNA-seq dataset with trimmomatic pre-processing, mapping to the longest (canonical) transcript from HG38 with kallisto, and DESeq2 with ActivePathways for pathway enrichment.

#### 29

Reference for mapping genomic data (illumina reads) to cancer genome for variant calling: https://docs.gdc.cancer.gov/Data/Bioinformatics_Pipelines/DNA_Seq_Variant_Calling_Pipeline/


#### 31 

Prepared for the meeting with Xi and Nick.

### April
#### 01

Started re-creating the first figure for oncochannels paper.

Re-downloaded the Reactome and Biological Processes from MSigDB. Re-ran pathways enrichment for cell line 729 with 10 - 500 composition.

#### 04

Main feedback from the pre-qualifying exam:
- Work harder on the figures
- Another dataset of interest in Toronto: Trevor Pew - GBM sequenced tumors


#### 05

Version 1 of fold-change figure:
- Paired bar plots per cancer
- One bar is p-value fc of outliers vs random groups of 271
- One bar is outlier rate p-value

#### 06

Worked on more versions of the fold change figure.

#### 10

Started panel 2 of the fold change figure.

Corrected scripts for processing GBM data.

#### 14

Finished first draft of Figure 1 for the ion channels project.

Started Figure 2.

#### 18

Look at cancer consensus gene list (CCG) for interesting differential expression of genes for Figure 3. 

Try controlling for age with the DESeq design matrix (low, medium, high).

Use Hox10AS in LGG as the control to make sure the hazard ratio is correct.

#### 19 

Continued updating and making figures for the ion channels project. 

#### 22 

Continued writing my proposal. 

#### 25

Continued working on the figures for the ion channels paper.

#### 26

Final revision for CPTM manuscript completed.

Sent draft of proposed aims out for review. 

#### 28

Completed draft progress to date for proposal. Started finalizing introduction.

#### 29

Completed draft introduction. Started finalizing figures.

### May
#### 02

Continued working on ion channel figures.

TCGA data online: https://gdc.cancer.gov/about-data/publications/pancanatlas

#### 03 

Focussed on pre-qualifying exam prep (studying).

#### 04 

Validating that the knock down of GBJ2 / SCN9A impacts the same genes.

#### 05

Practiced qualifying presentation. 

#### 09

Qualifying presentation in lab meeting. 

#### 10

Worked on DGEA for the cell lines and TCGA samples.

#### 11

Made figures for DGEA with edgeR using 1.25x FC (log2fc = 0.35) cut off.

#### 12

Worked on qualifying presentation.

Analyzed different between DESeq2 and EdgeR DGEA. EdgeR tends to identify more significant genes than DESeq2. However, DESeq2's p-values are often times inflated.

Started reviewing paper for Cell Review.

#### 13

Oncochannels meeting with Xi and Nick. 

Worked on redoing the assignment for ACBI with Zoe.

Improved qualifying presentation and started reviewing machine learning and stats textbook (Kieran Campbell recommendation).

#### 16

For combining p-values using Brown's Empirical Method: https://github.com/IlyaLab/CombiningDependentPvaluesUsingEBM/blob/master/Python/EmpiricalBrownsMethod.py

#### 17

Briefly summarized Cell Medicine paper for publication review.

Prepared and practiced for qualifying exam.

#### 18

Qualifying exam.

#### 19

Finished enriched pathway decomposition. 

#### 20

Finished enrichment map

#### 24

Finished Cell Medicine paper for publication review.

#### 27

8 unique GBM samples in TCGA have SCN9A mutations. 

#### 30

Implemented recommendations from the Friday meeting including:
- CGC genes enriched (considered as pathway for ActivePathways enrichment)
- Enrichment map merged neurons and epithelial group
    - Created another enrichment map with labels for this group

#### 31

Attended the synthetic biology symposium.

### June
#### 01

Completed revisions of the paper to review.

Visualized the expression of protein-coding genes in SCN9A / GJB2 high vs. low conditions of GBM samples and cell line KDs.

#### 02 

Started IC final figure versions in Photoshop.

Re-wrote methods section of the paper.

Started final code with new bin version. 

Looked up lncRNA paper for enriched CGC genes.

#### 20

VEP (vep / variant effect predictor) requires local databases or usage of ensembl at non-peak hours. 
Installed local GRCh38 database from: http://ftp.ensembl.org/pub/current_fasta/homo_sapiens/dna/

#### 21

Finished quick script for Marina.

#### 22

Worked on finalizing figures 1 and 2.

#### 23

Helped Marina execute the script. 

Executed snakemake for IC figures 1 - 3.

#### 24

Figures S2 and S3 visualized.

Meeting with Xi and Nick.

#### 25

Correlations for gene expression of all genes in all TCGA samples: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels_gbm/results/2022_02_28/_tcga_gene_expression/all_genes_correlations.tsv

#### 27

Presentation for lab WIP.

Collected TCGA GBM subtypes defined in Brennan et al. 2013 supplementary. 
Started investigating if there is a stronger association for a specific subtype in GJB2 / SCN9A high vs. low conditions.

#### 28

Starting looking at associations between high vs. low SCN9A / GJB2 expression and expression / methylation subtypes in Brennan et al. 2013.
Association between GJB2 outliers and mesenchymal / M1 (methylation) subtypes.

GSE84465 processed and meta-data downloaded to scRNA-Seq directory (/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels_gbm/data/scrna_seq/).

Single-Cell RNA-Seq Analysis of Infiltrating Neoplastic Cells at the Migrating Front of Human Glioblastoma
http://storage.googleapis.com/gbmseqrawdata/rawData.zip


GSE131928 also downloaded.

An integrative model of cellular states, plasticity and genetics for glioblastoma
https://singlecell.broadinstitute.org/single_cell/study/SCP393/single-cell-rna-seq-of-adult-and-pediatric-glioblastoma#/

Downloaded after logging in with:
curl --insecure "https://singlecell.broadinstitute.org/single_cell/api/v1/bulk_download/generate_curl_config?accessions=SCP393&auth_code=bqpdCUxN&directory=all&context=study"  -o cfg.txt; curl -K cfg.txt && rm cfg.txt

#### 29

Visualized GSE-84465. 

### July
#### 4

Downloaded scRNA seq data from the human brain motor cortex (https://portal.brain-map.org/atlases-and-data/rnaseq/human-m1-10x).

Downloaded scRNA seq data by cell type from the Human Protein Atlas (https://www.proteinatlas.org/about/download).

#### 5

Completed analysis of scRNA seq data from 2 GBM datasets (gse131928, gse84465) and the Human Protein Atlas whole brain tissue.

#### 6

Investigated survival curves from the different dichotomization methods for SCN9A and GJB2. 

Started addressing the age-related concerns for the groups by creating survival curves for all age ranges.

#### 11

Created and presented journal club: Glioma progression is shaped by genetic evolution and microenvironment interactions.

#### 12

Created survival curves for GLASS consortium data based on GJB2 / SCN9A expression. Does not look great.

#### 18

Started addressing the recommended comments from Jüri. 

#### 19

Downlaoded scRNA seq data from Johnson et al. 2021 (syn22257780). It contains 55284 cells and ~24,000 genes measured. https://www.synapse.org/#!Synapse:syn22257780/tables/

Acquired mutation status and subtypes of GBM data from biolinks and put in raw data directory for project.

#### 20

Finalized survival by Copy Number Alterations (no differences).
Poor correlations between copy number and expression.

Acquired Ivy Sub-tumoral expression data:
http://glioblastoma.alleninstitute.org/static/download.html
https://wiki.cancerimagingarchive.net/pages/viewpage.action?pageId=22515597#22515597a51d0dbc1efa484baa491e9d32333d03


#### 22

Analyzed Ivy sub-tumoral expression data.

Downloaded GBM BIOGRID interaction data of proteins known to be crucial in GBM evolution and pathogenicity from: https://downloads.thebiogrid.org/BioGRID/Release-Archive/BIOGRID-4.4.211/
Description: https://thebiogrid.org/project/5/glioblastoma.html

Downloaded current BIOGRID release for human cells.

Downloaded DepMap (dependency map) for cell lines (https://depmap.org/portal/download/).

NCAM1 is not profiled in DepMap.

#### 26

Downloaded co-essentiality data analysis of Achilles project from: A genome-wide atlas of co-essential modules assigns function to uncharacterized genes. Wainberg et al. 2021 (https://github.com/kundajelab/coessentiality), (https://www.nature.com/articles/s41588-021-00840-z).

#### 27

Sunit (Sorcha and Christine) has 28 patients with primary and recurrent blood and tissue. 

Finished network of various interactions.

#### 28

Installed synapseclient for Synapse data access on conda (genomic_exploratory).

Downloaded the scRNA-seq metadata from Johnson et al. 2021 (syn22257780) under the table section of Synapse.

### August
#### 02

Finalized the PPI network figure.

Finalized the Ivy Dataset summary.

#### 11

Worked on Figure S2.

#### 12

Figured out the Nick Huang's interesting genes for microtube / neuron projections are not annotated in pathway files for associated pathways.

Reminder that the productions folder for reimand lab is: /.mounts/labs/reimandlab/production

The data for the first 2018 sample for the GBM WGS sequencing is in: /.mounts/labs/reimandlab/production/190103_A00469_0021_AHFTV2DSXX/

#### 15

Use EPS and not PDF.

Descriptions for improvements of figures:
- S2: add the titles for each plot as bolded on top (make the differences between datasets more obvious)

Diogo sent code for visualizing CNA.

#### 16

Visualized CNA in SCN9A and IDH1 according to Diogo's script.

Improved figure S2.

#### 17

Complex heatmap: https://jokergoo.github.io/ComplexHeatmap-reference/book/a-single-heatmap.html

Started making heatmaps for CNA pearson and expression spearman correlations for CGC genes with Rho > 0.7 to the top 5 ion channels: AQP9, GJB2, GJD3, KCNN4, SCN9A.

#### 18

Re-created conda environments in miniconda because base conda had problems.

#### 23

Created new scRNA plots.

#### 24

Worked on Figure S4 adding immune associations.

Created Figure S5 to separate enrichment of ion channels in different regions of the tumor / brain vs. S4 for pathway enrichment.

#### 26

Re-ran the elastic net analysis accounting for the type of clinical outcome that is recommended for the specific cancer. This was determined by adjusting the "tcga_abbreviations.tsv" reference file according to the Smith et al. 2022 publication.

#### 29

Worked intimately with Juri to finalize ion channel figures.


### September
#### 05

Human kinases: http://kinase.com/web/current/kinbase/genes/SpeciesID/9606/

#### 06

We can use HGNC to identify ion channels (https://www.genenames.org/data/genegroup/#!/group/177) and GPCRs (https://www.genenames.org/data/genegroup/#!/group/139) but not kinases because that family is more diverse. 

#### 07

Downloaded human gene information (gene_info.gz) from NCBI (ftp://ftp.ncbi.nlm.nih.gov/gene/DATA/gene_info.gz) and created a translation for symbol to aliases (https://www.biostars.org/p/1378/).

#### 08

Downloaded kinases from uniprot (email from Jüri):
ion channels: 325 / 327
gpcrs: 754 / 805
kianses: 511 / 519

Translated ensembl IDs to entrez IDs with biomart by following: https://www.biostars.org/p/398563/#398619

Subset GPCRs and Ion channel to only those with protein products.
Ion channels: 325 / 327 (not included are GJE1 and KCNJ18)
GPCRs: 754 / 805 (not included include GPR33, GPR42, TAS2R33, and several unnamed “OR…” GPCRs)

Started using Juri's 

#### 09

By adding the Guide to Pharmacology gene class annotations:
ion channels: 335 / 338
gpcrs: 767 / 823
kianses: 511 / 519

Druggable:
Ion channels: 81% (273 / 335) are druggable
GPCRs: 46% (353 / 767) are druggable
Ion channels: 95% (485 / 511) are druggable

#### 15

For human and mouse protein kinases: https://www.uniprot.org/help/mouse

Specific file: https://ftp.uniprot.org/pub/databases/uniprot/current_release/knowledgebase/complete/docs/pkinfam

#### 20

Followed a tutorial to uninstall LaTeX: https://tug.org/mactex/uninstalling.html


Downloaded manually extended manifest for Illumina 450k methylation data from: https://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?acc=GPL18809

Downloaded illumina methylation data from https://emea.support.illumina.com/downloads/infinium_humanmethylation450_product_files.html

Based on: https://emea.support.illumina.com/bulletins/2016/08/infinium-methylationepic-manifest-column-headings.html


#### 29

To subset to TCGA expression data from GDC:
```files.data_category in ["transcriptome profiling"] and files.data_format in ["tsv"] and files.data_type in ["Gene Expression Quantification"] and cases.project.program.name in ["TCGA"] and files.experimental_strategy in ["RNA-Seq"]```

### October
#### 03

Note that the ensembl transcripts for HG38 were downloaded from (http://useast.ensembl.org/info/data/ftp/index.html/) and subset to canonical transcripts from: https://www.ensembl.org/biomart/martview/5795cb27210be4d38345d7d8d73b1fb4

#### 05

Downloaded RNA sequencing for HG19 in two separate batches (protein-coding and lncRNAs) from: https://www.gencodegenes.org/human/release_19.html

The two separate downloads were concatenated into: /.mounts/labs/reimandlab/private/users/abahcheli/human_genome/gencode.v19.transcripts.fa.gz 

Downloaded per-sample data from GTEx (https://gtexportal.org/home/<USER>/.mounts/labs/reimandlab/private/users/abahcheli/additional_data/gtex/GTEx_Analysis_2017-06-05_v8_RSEMv1.3.0_transcript_tpm.gct.gz
And the description of each sample: GTEx_Analysis_v8_Annotations_SubjectPhenotypesDS.txt


#### 20

Package to break the y-axis in ggplot:
https://cran.r-project.org/web/packages/ggbreak/vignettes/ggbreak.html


Label only the genes from D in A (at least all labels from D in A)
Include the 5th label in fig 2 panel b


#### 26 

Reminder GJB2 has no missense mutations in TCGA, SCN9A has 8, one sample has 3 mutations (3% of cohort).

### November
#### 10

TPM and raw counts of TCGA data was acquired from Masroor's directory: /.mounts/labs/reimandlab/private/users/mbayati/prognostic_immunogenomic_markers/data/GDCdata/TCGA-GBM/harmonized/Transcriptome_Profiling/expression_data.rds

Copied to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels_gbm/data/raw_data/TCGA_GBM_tpm.tsv


Note Ivy Sub-tumoral expression data (FPKM): https://glioblastoma.alleninstitute.org/api/v2/well_known_file_download/305873915
Downloaded to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels_gbm/data/ivy_subtumor/

#### 11

Incidences of GBM can be tracked from Supplementary Table 8 of: CBTRUS Statistical Report: Primary Brain and Other Central Nervous System Tumors Diagnosed in the United States in 2015–2019

#### 17

Note: Synapse scRNA data (/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels_gbm/data/scrna_seq/syn22257780) copied to /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels_gbm/data/raw_data/scrna/

Same with Broad scRNA data: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels_gbm/data/scrna_seq/gse131928/

#### 18

Submit expenses for reimbursement at: certify.oicr.on.ca 


#### 22

Note: we only used 729 cell for DGEA because there was virtually no SCN9A expression in 797.


	797_S3_a	797_S3_c	797_G5_a	797_Scr_b	797_S3_b	797_Scr_c	797_G5_b	797_Scr_a	797_G5_c
GJB2	0.343495	0.371277	0.119056	2.151900	0.525383	3.042840	0.030945	2.257780	0.128731
SCN9A	0.151044	0.158382	0.119921	0.177408	0.117068	0.216143	0.105691	0.219173	0.120360


#### 23

Downloaded the hg19 canonical transcript list from UCSC genome browser table. 
Feb. 2009 (BRCh37/hg19) | Genes and Gene Predictions | UCSC Genes | knownCanonical

Then downloaded the translation for UCSC genes to ensembl (https://www.biostars.org/p/9474500/):
https://hgdownload.soe.ucsc.edu/goldenPath/hg19/database/knownToEnsembl.txt.gz

#### 28

Consider the Recount2 database which has methods for combining data.

### December
#### 06

Jüri's AACR membership: 375452
Jüri's AACR email: <EMAIL>

#### 26

The Broad institutes docker for GATK (https://gatk.broadinstitute.org/hc/en-us/categories/360002302312): https://hub.docker.com/r/broadinstitute/gatk/tags

Followed the GDC workflows for mRNA sequence alignment and counts https://docs.gdc.cancer.gov/Data/Bioinformatics_Pipelines/Expression_mRNA_Pipeline/

GTF for HG19 from: https://www.gencodegenes.org/human/release_19.html
Used the "Comprehensive gene annotation" file for mapping. 

#### 31

Harvard STAR tutorial: https://hbctraining.github.io/main/


## 2023
### January
#### 03

Tutorial for Nina's TP53 mapping of mutations: https://support.bioconductor.org/p/75497/

#### 06
Solve kernel errors: https://stackoverflow.com/questions/61121610/error-in-jupyter-notebook-what-could-be-the-reason


#### 18

Note that starting docker containers on HPC fail to add the whole path required: 
export PATH="/usr/bin:/usr/local/bin:$PATH"

Mouse genome fasta (https://www.gencodegenes.org/mouse/): https://ftp.ebi.ac.uk/pub/databases/gencode/Gencode_mouse/latest_release/GRCm39.genome.fa.gz

Tutorials for GATK: https://gatk.broadinstitute.org/hc/en-us/sections/360007226631-Tutorials


Data pre-processing and mapping pipeline used: https://gatk.broadinstitute.org/hc/en-us/articles/360039568932


Variant calling pipeline used: https://gatk.broadinstitute.org/hc/en-us/articles/360035894731-Somatic-short-variant-discovery-SNVs-Indels-


#### 19

For reference, the current pipline is now: https://broadinstitute.github.io/warp/docs/Pipelines/Whole_Genome_Germline_Single_Sample_Pipeline/README/

Creating a normal reference for mouse samples requires having a database of mice variants: https://gatk.broadinstitute.org/hc/en-us/community/posts/360069566651-Mutect2-germline-resources-for-mm10


#### 20

Tutorial for running GATK workflows: https://gatk.broadinstitute.org/hc/en-us/articles/360035530952

#### 22

Mouse variant database from: https://github.com/igordot/genomics/blob/master/workflows/gatk-mouse-mm10.md

Mouse variant database chat: https://www.biostars.org/p/338288/
Mouse variant resources: http://uswest.ensembl.org/Mus_musculus/Info/Index

Mouse variants in vcf format: https://www.sanger.ac.uk/data/mouse-genomes-project/

#### 25

Installed Samtools from source (https://www.htslib.org/download/) and made the file. Following concessions made:
```
./configure --prefix=/.mounts/labs/reimandlab/private/users/abahcheli/software/ --without-curses --disable-bz2 --disable-lzma
```

Fix bam file issues: https://sites.google.com/a/broadinstitute.org/legacy-gatk-forum-discussions/2014-09-09-2014-03-21/4290-SAM-bin-field-error-for-the-GATK-run

#### 26

Exome bed file for mouse from twist: https://www.twistbioscience.com/resources/data-files/twist-mouse-exome-panel-bed-file

To subset vcf to specific bed file: https://www.biostars.org/p/46331/

You can annotate variants using ANNOVAR: https://annovar.openbioinformatics.org/en/latest/user-guide/download/

Another option is Jannovar: https://doc-openbio.readthedocs.io/projects/jannovar/en/master/annotate_vcf.html

Instructions for making an ANNOVAR database: https://annovar.openbioinformatics.org/en/latest/user-guide/gene/#create-your-own-gene-definition-databases-for-non-human-species
Under the "Create your own gene definition databases for non-human species".


Used Annovar: 
```
Here is the link to download ANNOVAR: http://www.openbioinformatics.org/annovar/download/0wgxR2rIVP/annovar.latest.tar.gz

Please cite ANNOVAR paper in your publication: Wang K, Li M, Hakonarson H. ANNOVAR: Functional annotation of genetic variants from next-generation sequencing data, Nucleic Acids Research, 38:e164, 2010
```

#### 27

Nanoplot to summarize results from the pilot nanopore sequencing. 

#### 30

Minimap2 alignment: https://lh3.github.io/minimap2/minimap2.html

Recommended nanopore variant calling: https://github.com/HKU-BAL/Clair3

#### 31

Reactome and Biological Processes gmt files can be downloaded from GProfiler: https://biit.cs.ut.ee/gprofiler/gost

Recommended nanopore structural variant calling: https://github.com/fritzsedlazeck/Sniffles

### February
#### 01

Use "bedtools subtract" to subtract germline variants.

#### 03

samtools tview command
```
export COLUMNS ; /.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools tview -d T -p chr14:31023110 --reference /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ns_snvs_nina/data/mm10_wgs_analysis/mm10.fa 46775_HR_dgApobec3_Tumor_3_S9-merged_alignment.bam > /.mounts/labs/reimandlab/private/users/abahcheli/tmp/mm10_vcfs/46775_HR_Notch_Tumor_1_S9_chr14_31023124.txt
```

#### 16

Have to programmatically find the distribution with the median of logFC and expression and select it to show.

- Order the panels to grow with significant difference

#### 28

GSE4412 https://aacrjournals.org/cancerres/article/64/18/6503/511717/Gene-Expression-Profiling-of-Gliomas-Strongly  Check GSE4412.xlsx

GSE43378 https://onlinelibrary.wiley.com/doi/10.1111/cas.12214  Check GSE43378.xlsx

### March
#### 03

Run docker container on singularity: eg Guppy
```
module load singularity
OR
conda activate singularity-env

singularity instance start docker://genomicpariscentre/guppy-gpu guppy
```

#### 07

Covid ptm figures: https://www.dropbox.com/sh/g37qzxki75dlp6b/AAAY5QHI16AvY-RMFBgTF3Nsa/figures?dl=0&subfolder_nav_tracking=1

#### 08

Singularity on the HPC: https://pawseysc.github.io/singularity-containers/12-singularity-intro/index.html

Singularity executions: https://docs.sylabs.io/guides/2.6/user-guide/singularity_and_docker.html

#### 13

Build singularity images locally for use:
```
module load singularity

export SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/

/.mounts/labs/resit/modulator/sw/Ubuntu18.04/singularity-3.8.2/bin/singularity pull /.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-4.3.0.0/gatk_4.3.0.0.sif docker://broadinstitute/gatk

singularity pull varscan2.sif docker://jeltje/varscan2


```

#### 15

Subtract one VCF from another:
https://www.biostars.org/p/279359/


- Go after some of the GBM loci
- Boxplot of lengths of SVs from sniffles


- Fig5:
	- Make the width of the page
	- Make sure the imageJ quantity is the same


#### 20

GBM specific networks.

/.mounts/labs/reimandlab/private/users/abahcheli/tmp/GDCdata/TCGA-GBM/harmonized/DNA_Methylation/Methylation_Beta_Value

### May 
#### 05

Illumina methylation data: https://emea.support.illumina.com/array/array_kits/infinium_humanmethylation450_beadchip_kit.html

#### 10 

GDC improved methylation data: https://gdc.cancer.gov/content/improved-dna-methylation-array-probe-annotation

(EpicV2)

#### 19

GATK sites of known variance VCF: https://console.cloud.google.com/storage/browser/gcp-public-data--broad-references/hg38/v0;tab=objects?prefix=&forceOnObjectsSortingFiltering=false

Reference for which sites to use: https://gatk.broadinstitute.org/hc/en-us/community/posts/360075305092-Known-Sites-for-BQSR

#### 21

MuSe: https://bioinformatics.mdanderson.org/public-software/muse/
Strelka: https://github.com/Illumina/strelka
VarScan2: https://github.com/Jeltje/varscan2
Mutect2: https://gatk.broadinstitute.org/hc/en-us/articles/360037593851-Mutect2
SomaticSniper: 

GDC standard workflows: https://github.com/NCI-GDC/gdc-somatic-variant-calling-workflow

#### 23

Built custom singularity container for guppy.

```
export SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/

/.mounts/labs/resit/modulator/sw/Ubuntu18.04/singularity-3.8.2/bin/singularity pull /.mounts/labs/reimandlab/private/users/abahcheli/software/guppy/guppy_gpu_v3.sif docker://abahcheli/guppy-gpu

singularity run --bind /.mounts,/usr /.mounts/labs/reimandlab/private/users/abahcheli/software/guppy/guppy_gpu_v3.sif /bin/bash
```

Finally figured out basecalling on the HPC. Main notes:
- Make sure to call a GPU instance correctly
- Singularity with the GPU instance requires properly mounting the guppy_basecaller AND the nvidia / CUDA drivers. Nvidia drivers are all in /usr and default guppy_basecaller is in /usr/bin
    - I made a custom docker image (abahcheli/guppy-gpu:latest) that copies /usr/bin to /ont-guppy. I built the singularity image around this /.mounts/labs/reimandlab/private/users/abahcheli/software/guppy/guppy_gpu_v3.sif


#### 29

Changed snakemake conda env like this: https://github.com/snakemake/snakemake/pull/1708/files
According to this: https://github.com/snakemake/snakemake/pull/1708

File is: /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/snake_env/lib/python3.11/site-packages/snakemake/deployment/conda.py

### June 
#### 02

For some unknown reason, MuSE2 installed this time.

#### 05

Sites of known variation from dbSNP can be acquired at: https://www.ncbi.nlm.nih.gov/variation/docs/human_variation_vcf/

hg38 centromeres bed file from: https://github.com/BD2KGenomics/dockstore_workflow_snps/blob/master/hg38.centromere.bed

exome bed file from: https://emea.support.illumina.com/downloads/enrichment-bed-files-hg38.html

human genome information from: https://www.gencodegenes.org/human/

#### 06

For the Taylor lab pathway analysis, gmt files downloaded from: https://biit.cs.ut.ee/gprofiler/gost

All transcription factors from: http://humantfs.ccbr.utoronto.ca/download.php
HUGO general transcription factors from: https://www.genenames.org/data/genegroup/#!/group/565
Also searched for all "GTF"-labelled genes in proteomics and phospho-proteomics data.

#### 07

Started a BRCA CNA collaboration with Schramek lab. 

G:profiler from https://cran.r-project.org/web/packages/gprofiler2/vignettes/gprofiler2.html#installation-and-loading

Detailed information from https://cran.r-project.org/web/packages/gprofiler2/gprofiler2.pdf

#### 09

Instructions to curl download files from transfer.oicr.on.ca: https://man.liquidfiles.com/api/

```sh
# info about files
curl -s -X GET --user "juyrrnjiuL1jrQ2cf16KOb:x" -H "Accept: application/json" -H "Content-Type: application/json" https://transfer.oicr.on.ca/message/P2yOBhhgYl3ARF2HdMXzgv

# copy files from each "url" link
curl -s -X GET --user "juyrrnjiuL1jrQ2cf16KOb:x" -O https://transfer.oicr.on.ca/messages/vkuA2d5ojr0flo65oo6NJi/attachments/ZPnIZx7AZhkY8Bkauj5aky/download/ovE6J4QsqEMe1ikneTzCTF/KA_WGS2_S2_R1_001.fastq.gz
```

Mouse genome annotations from gencode.
Mouse SNPs and Indels from: https://www.sanger.ac.uk/data/mouse-genomes-project/
Mice / mouse has telocentric chromosomes.

CNA software:
- https://bmcgenomics.biomedcentral.com/articles/10.1186/s12864-021-07686-z#Sec2
- https://www.ncbi.nlm.nih.gov/pmc/articles/PMC8071346/#B6-diagnostics-11-00708
- https://www.ncbi.nlm.nih.gov/pmc/articles/PMC8699073/

#### 12

Translate Strelka output to VCF for ANNOVAR by following these instructions: https://github.com/WGLab/doc-ANNOVAR/issues/64

Converted vcf files to maf files.
Started pyclone analysis on maf files. 

#### 13

Analyzed BRCA data using:
CNVnator
CNVkit - refFlat.txt from https://googlegenomics.readthedocs.io/en/latest/use_cases/discover_public_data/ucsc_annotations.html

#### 15

BICseq2 mappability of mouse chromosomes from here: https://bismap.hoffmanlab.org

#### 19

PCAWG evolution from: https://www.nature.com/articles/s41586-019-1907-7#data-availability
PCAWG pipeline: https://github.com/PCAWG-11/Evolution
PCAWG data: https://dcc.icgc.org/releases/PCAWG/evolution_and_heterogeneity


Nanopore sequencing summary: https://docs.google.com/spreadsheets/d/1F4ZUI8E0QPUNeUTtKPagMS2qsUVFtiG916bgWOentEg/edit#gid=0

#### 21

Varscan2 extract hg38 exons: https://www.biostars.org/p/459269/ 
```shell
awk '($3=="exon") {printf("%s\t%s\t%s\n",$1,int($4)-1,$5);}' gencode.v43.gtf |\
sort -T . -t $'\t' -k1,1 -k2,2n | bedtools merge > hg38_exons.bed
```
From the gtf /.mounts/labs/reimandlab/private/users/abahcheli/human_genome/gencode.v43.gtf

#### 22

Annovar for Mutect2 and Strelka: https://bioinformaticsdotca.github.io/bicg_2018_lab_7

#### 27

All GBM mutations for Michael figure 5 APW2 paper from: https://portal.gdc.cancer.gov/genes/ENSG00000138413?filters=%7B%22op%22%3A%22and%22%2C%22content%22%3A%5B%7B%22op%22%3A%22in%22%2C%22content%22%3A%7B%22field%22%3A%22cases.available_variation_data%22%2C%22value%22%3A%5B%22ssm%22%5D%7D%7D%2C%7B%22content%22%3A%7B%22field%22%3A%22cases.project.project_id%22%2C%22value%22%3A%5B%22TCGA-GBM%22%5D%7D%2C%22op%22%3A%22in%22%7D%2C%7B%22op%22%3A%22NOT%22%2C%22content%22%3A%7B%22field%22%3A%22ssms.ssm_id%22%2C%22value%22%3A%22MISSING%22%7D%7D%5D%7D

nsSNVs: Created plots showing segments come with age and VAFs come with age
- Do the CNVs correlate with the VAFs? Not really

Schramek lab: built genome-wide visualizations as circos plots and showed each chromosome individually
- Doesn't matter what the gain is as long as it's a gain


#### 30

Genome mappability repo for custom mappability tracks: https://github.com/shahcompbio/hmmcopy_utils


### July
#### 17

Synapse client username: abahcheli password: [the usual]

TCGA tumor purity: https://www.nature.com/articles/ncomms9971#Sec14
/.mounts/labs/reimandlab/private/generated_raw_data/TCGA_PanCanAtlas/tcga_purity_estimates.tsv

PCAWG: /.mounts/labs/reimandlab/private/generated_raw_data/PCAWG/consensus.20170217.purity.ploidy.txt.gz

#### 18

Downloading data from ICGC: https://docs.icgc.org/download/repositories/
Data of interest: https://dcc.icgc.org/repositories?filters=%7B%22file%22:%7B%22dataType%22:%7B%22is%22:%5B%22SSM%22%5D%7D,%22experimentalStrategy%22:%7B%22is%22:%5B%22WGS%22%5D%7D,%22fileFormat%22:%7B%22is%22:%5B%22VCF%22%5D%7D%7D%7D&files=%7B%22from%22:1,%22size%22:25%7D


#### 20

nsSNVs Nina project files:
```R
PCAWG = read.delim("/.mounts/labs/reimandlab/private/users/nadler/projects/mutational_signatures/data/output/V3/001.1_process_PCAWG_variants/G_PCAWG_exonic_variants_with_signatures.tsv")
HMF = read.delim("/.mounts/labs/reimandlab/private/users/nadler/projects/mutational_signatures/data/output/V3/001.2_process_HMF_variants/F_HMF_exonic_variants_with_signatures.tsv")
TCGA = read.delim("/.mounts/labs/reimandlab/private/users/nadler/projects/mutational_signatures/data/output/V3/001.7_process_TCGA_variants2/F_TCGA_exonic_variants_with_signatures.tsv")
```

Clonality files:
HMF: /.mounts/labs/reimandlab/private/users/jreimand/SigCode/data/160322/mutations_HMF_clonality/HMF_muts_with_clonality.rsav
TCGA: /.mounts/labs/reimandlab/private/generated_raw_data/TCGA_PanCanAtlas/SNVs_indels/TCGA_consolidated.abs_mafs_truncated.fixed.txt.gz

#### 24

GJB2 and SCN9A mutations: https://portal.gdc.cancer.gov/exploration?facetTab=genes&filters=%7B%22op%22%3A%22and%22%2C%22content%22%3A%5B%7B%22op%22%3A%22in%22%2C%22content%22%3A%7B%22field%22%3A%22cases.available_variation_data%22%2C%22value%22%3A%5B%22ssm%22%5D%7D%7D%2C%7B%22content%22%3A%7B%22field%22%3A%22cases.project.project_id%22%2C%22value%22%3A%5B%22TCGA-GBM%22%5D%7D%2C%22op%22%3A%22in%22%7D%2C%7B%22op%22%3A%22in%22%2C%22content%22%3A%7B%22field%22%3A%22genes.gene_id%22%2C%22value%22%3A%5B%22ENSG00000165474%22%2C%22ENSG00000169432%22%5D%7D%7D%5D%7D&searchTableTab=genes

```python
scn9a_mutant_ids = ['TCGA-12-3649', 'TCGA-19-2631', 'TCGA-81-5910', 'TCGA-06-0132', 'TCGA-06-0169', 'TCGA-06-5416', 'TCGA-12-0821', 'TCGA-27-2519', 'TCGA-26-5136', 'TCGA-12-3644', 'TCGA-06-0213', 'TCGA-16-0846', 'TCGA-06-0881', 'TCGA-06-0155', 'TCGA-32-2616']
```

### August
#### 2

IP to do:
- Draft response to reviewer
- Figure legends
- Update tables 

#### 16

ABSOLUTE for copy number analysis: https://github.com/genepattern/ABSOLUTE/tree/main

Novel implementation of ABSOLUTE: https://github.com/tgac-vumc/ACE

#### 21

Built a new docker for Absolute from the docker recipe in the repo (abahcheli/genepattern-absolute). Pushed to docker hub.

Downloaded as a singularity image on the HPC.

#### 22

Use CNVkit for normal-tumor CNV analysis: https://cnvkit.readthedocs.io/en/stable/quickstart.html
Supported by this thread: https://www.biostars.org/p/401526/


#### 23

Downloaded SRA data for https://www.nature.com/articles/s41586-022-05082-5#code-availability from https://www.ncbi.nlm.nih.gov/sra (project code: PRJNA718334).

- Downloaded metadata from: https://trace.ncbi.nlm.nih.gov/Traces/study/?page=50&acc=SRP312636&o=acc_s%3Aa

- Subset to "Organism" "[mM]us", 'Sample Name' to '[bB]ulk'

- Created a list of unique "REPLICATES" where "dev_stage" includes both "Tumor" and "Pre"

- Downloaded those from the SRA: https://www.ncbi.nlm.nih.gov/sra?linkname=bioproject_sra_all&from_uid=718334


### September
#### 05

Downloaded docker container onto HPC for processing Nature CNV data.
```
singularity pull /.mounts/labs/reimandlab/private/users/abahcheli/software/nature_cnvs/p53_loh.sif docker://krasnitzlab/p53-loh-figures
```

#### 06

mm39 centromere and telomere bed files from: https://genome.ucsc.edu/cgi-bin/hgTables?db=mm39&hgta_group=map&hgta_track=gap&hgta_table=gap&hgta_doSchema=describe+table+schema

- Go to https://hgdownload.soe.ucsc.edu/goldenPath/mm39/database/

- Download file gap.txt.gz

#### 14

Nanopore sequencing schematic from: https://zenodo.org/record/4636844

#### 21 

HMMcopy worked by creating a custom script to turn the .seg file for read counts into a wig file for importing. 

### October
#### 03

For creating Manhattan plots: https://r-graph-gallery.com/101_Manhattan_plot.html

Battenburg CNV predictions: https://github.com/Wedge-lab/battenberg

Schramek lab CNV analysis: https://www.nature.com/articles/s41586-022-05082-5#data-availability
Data from: https://www.ncbi.nlm.nih.gov/bioproject/PRJNA718334/

Biorender alternatives: https://bioicons.com

#### 25

GBM SNV callers:
- Mutect2
- Strelka
- Muse2
- Platypus

GBM SV callers:
- DELLY (PCAWG) https://academic.oup.com/bioinformatics/article/28/18/i333/245403
- 

https://www.nature.com/articles/nbt.3027

Tools for analyzing cancer data: https://github.com/cancerit


Use a paired analysis for DGEA


#### 26

Methods for increasing MarkDuplicatesSpark efficacy:
- https://github.com/broadinstitute/gatk/issues/8307
- https://gatk.broadinstitute.org/hc/en-us/community/posts/4445958815003-MarkDuplicatesSpark-speed-estimates
- https://gatk.broadinstitute.org/hc/en-us/community/posts/360058452072-MarkDuplicatesSpark-consumes-enormous-amount-of-RAM?page=1#community_comment_4404674058267


*PCAWG SNV callers*

CASM IT (https://github.com/cancerit Sanger):
- EXCLUDED - Raine, K. M. et al. ascatNgs: identifying somatically acquired copy-number alterations from whole-genome sequencing data. Curr. Protoc. Bioinformatics 56, 15.9.1–15.9.17 (2016).
    - SNVs, R package and lots of pre-processing

- Raine, K. M. et al. cgpPindel: identifying somatically acquired insertion and deletion events from paired end sequencing. Curr. Protoc. Bioinformatics 52, 15.7.1–15.7.12 (2015).
    - Indels, requires lots of pre-processing

- Jones, D. et al. cgpCaVEManWrapper: simple execution of CaVEMan in order to detect somatic single nucleotide variants in NGS data. Curr. Protoc. Bioinformatics 56, 15.10.1–15.10.18 (2016).
    - SNVs, lots of dependencies


EMBL
- Rausch, T. et al. DELLY: structural variant discovery by integrated paired-end and split-read analysis. Bioinformatics 28, i333–i339 (2012).
    - SVs

- Rimmer, A. et al. Integrating mapping-, assembly- and haplotype-based approaches for calling variants in clinical sequencing applications. Nat. Genet. 46, 912–918 (2014).
    - SNVs (Platypus), old (>6yrs)

BROAD
- Cibulskis, K. et al. Sensitive detection of somatic point mutations in impure and heterogeneous cancer samples. Nat. Biotechnol. 31, 213–219 (2013).
    - SNVs (Mutect2)

- Carter, S. L. et al. Absolute quantification of somatic DNA alterations in human cancer. Nat. Biotechnol. 30, 413–421 (2012).
    - CNAs (ABSOLUTE) https://software.broadinstitute.org/cancer/cga/absolute

- Drier, Y. et al. Somatic rearrangements across cancer reveal classes of samples with distinct patterns of DNA breakage and rearrangement-induced hypermutability. Genome Res. 23, 228–235 (2013).
    - SVs?

- Ramos, A. H. et al. Oncotator: cancer variant annotation tool. Hum. Mutat. 36, E2423–E2429 (2015).
    - SNVs and indels (Oncotator)

Others:
- Moncunill, V. et al. Comprehensive characterization of complex structural variations in cancer by directly comparing genome sequence reads. Nat. Biotechnol. 32, 1106–1112 (2014).
    - SNVs (https://github.com/smufin/smufin-core)

- Fan, Y. et al. MuSE: accounting for tumor heterogeneity using a sample-specific error model improves sensitivity and specificity in mutation calling from sequencing data. Genome Biol. 17, 178 (2016).
    - SNVs (MuSE2)

*Other tools*
- Structural variation analysis of 6,500 whole genome sequences in amyotrophic lateral sclerosis
    - SVs (Manta)

Structural variant calling
- https://genomebiology.biomedcentral.com/articles/10.1186/s13059-019-1720-5#Abs1

https://www.ncbi.nlm.nih.gov/pmc/articles/PMC4383288/


#### 27

Somatic variants review: https://www.ncbi.nlm.nih.gov/pmc/articles/PMC5852328/#:~:text=The%20majority%20of%20current%20somatic,using%20the%20matched%20normal%20sample.

Possible tools for analyzing WGS data:

*Tools for analyzing SNVs*
- Mutect2
- Strelka
- MuSE2
- VarScan2

- Platypus

*Tools for analyzing SVs*
- DELLY2
- CNVnator
- Pindel
- CNVkit

- Battenberg
- Manta
- Smufin

*Tools for analyzing CNAs*
- Control-FREEC
- HMMcopy
- CNVkit

- Battenberg
- Smufin

#### 31

Sarek variant calling pipeline: https://github.com/nf-core/sarek

ASCAT                   Yes     Tumor purity and ploidy*
CNVkit                  Yes     Copy number variation
Control-FREEC           Yes     Copy number variation
DeepVariant             Yes     Single nucleotide variants*
freebayes               Yes     Haplotype-based variant detection*
GATK HaplotypeCaller    Yes     Haplotype-based variant detection*
Manta                   Yes     Structural variants
mpileup
MSIsensor-pro
Mutect2                 Yes     Single nucleotide variants
Sentieon Haplotyper
Strelka2                Yes     Single nucleotide variants
TIDDIT                  Yes     Structural variants from WGS

### November
#### 13

DeepVariant calling is for germline variants.

#### 14

Downloaded docker image for CPG-WGS pipeline from: https://dockstore.org/containers/quay.io/wtsicgp/dockstore-cgpwgs:2.1.0?tab=info

Execution descriptions in: https://github.com/cancerit/dockstore-cgpwgs/wiki/Running-under-docker 
and
https://github.com/cancerit/dockstore-cgpwgs#usage

ASCAT and Battenburg described here: https://www.crick.ac.uk/research/labs/peter-van-loo/software

ASCAT tutorial: https://www.ncbi.nlm.nih.gov/pmc/articles/PMC6097604/#S8


__Tools decided__
*SNVs*
- Mutect2 (GATK image)          DONE
- Strelka (snv_callers)         DONE
- MuSE2 (software)              DONE
- Octopus (octopus)             DONE
- VarScan2 (software)           DONE

*SVs and CNAs*
- CNVnator (cnvnator)           DONE
- CNVkit (cnv_callers)          DONE
- Control-FREEC (cnv_callers)   DONE
- HMMcopy (hmm_copy)            DONE
- DELLY2 (delly)                DONE
- Manta

__Other tools__
*Other SNVs*
- Platypus                      No somatic variants calling
- Caveman ()                    Requires knowledge of CNAs ahead of time
- Pindel ()                     Requires extensive pre-processing and CNA information

*Other SVs and CNAs*
- ASCAT

- Manta
- TIDDIT

- Battenberg
- Smufin

#### 16 

Started Nick's metastatic project. 

#### 21

Look for tandem repeats using different tools than Epi2ME (eg. Tandem Repeat Finder)
- Use graphaligner to find repeats and their sizes
- ExpansionHunter de novo to find repeats but not their sizes

#### 22

Nick's collaboration Xi PIEZO1:
Nothing better than paired analysis because of subtypes.

Also look at TCGA whether it metastasizes or not.

Mets have more mutations in general. 

Harder to compare cohorts, easier to say that within a cohort there are more mutations than expected by chance. Maybe run ActiveDriver on it.

Expression is freely accessible.

#### 27

TRACERX cohort: access requested.

### December
#### 05

Annovar: used the ensGene database for hg38 from https://annovar.openbioinformatics.org/en/latest/user-guide/download/

#### 11

Annotate genes separately for CNVkit.

Start with visualization of CNVs.

#### 13

Try running dN/dS on mutation rates.

Look at where the mutations occur.

#### 14 

Commands to build ANNOVAR databases:
```sh
cd /.mounts/labs/reimandlab/private/users/abahcheli/software/annovar

./annotate_variation.pl --downdb refGene humandb3 -build hg38
./annotate_variation.pl --buildver hg38 --downdb seq humandb3/hg38_seq
./retrieve_seq_from_fasta.pl humandb3/hg38_refGene.txt -seqdir humandb3/hg38_seq -format refGene -outfile humandb3/hg38_refGeneMrna.fa
```

Then run ANNOVAR:
```sh

/.mounts/labs/reimandlab/private/users/abahcheli/software/annovar/annotate_variation.pl -geneanno -dbtype refGene -buildver hg38 /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/wgs_processing/RLGS1-primary_wgs_seq/RLGS1-primary_wgs_seq-common_snvs.vcf /.mounts/labs/reimandlab/private/users/abahcheli/software/annovar/humandb_v3/
```

## 2024
### January
#### 10

Snakemake standardized workflows: https://snakemake.github.io/snakemake-workflow-catalog/?usage=snakemake-workflows%2Fdna-seq-gatk-variant-calling

#### 30

To classify GBM subtypes:
https://bmccancer.biomedcentral.com/articles/10.1186/s12885-022-09191-2#Abs1

### February
#### 06

To run STAR-Fusion: https://github.com/STAR-Fusion/STAR-Fusion/wiki/installing-star-fusion#preparing-the-genome-resource-lib

Don't forget to install the genome library for HG38: https://data.broadinstitute.org/Trinity/CTAT_RESOURCE_LIB/__genome_libs_StarFv1.10/GRCh38_gencode_v37_CTAT_lib_Mar012021.source.tar.gz

Used the non-T2T file for STAR-Fusion because working with WGS data.


#### 14

Varscan2 CNV results:
- Deletion, mean ≤ -0.3
- Normal, mean -0.3 ≤ 0.3
- Gain, mean ≥ 0.3

HMMcopy results:
- HOMD Homozygous deletion, ≤ 0 copies
- HETD Heterozygous deletion, 1 copy 
- NEUT Neutral change, 2 copies
- GAIN Gain of chromosome, 3 copies
- AMPL Amplification event, 4 copies
- HLAMP High level amplification, ≥ 5 copies

#### 24

*APW2 analysis*

Gene expression and methylation from GLASS cohort:
- Subset GLASS to non-TCGA samples of gliomas stage 3 - 4 only
- 69 samples had methylation: 6 were IDH mutant
- 150 samples had gene expression data: 14 were IDH mutant

Proteomics:
https://www.nature.com/articles/s41467-020-17139-y#data-availability
Downloaded protein expression data from: https://proteomecentral.proteomexchange.org/?search=PXD015545


Quantitative proteomics from https://www.nature.com/articles/s41467-020-17139-y#data-availability:
- 3909 proteins profiled
- 54 samples have protein expression values
- 6 samples are IDH mutant (R132H)

### March
#### 06

To do for the WGS analysis:
- Cibersort
- Sigprofiler*
- PyClone*

### April
#### 02

Modified base calling into BAM format with dorado: https://github.com/nanoporetech/dorado

#### 09

MetaSV for short-read illumina WGS analysis: http://bioinform.github.io/metasv/

#### 11

Phasing CNAs using CHISEL: https://github.com/raphael-group/chisel

#### 22

GATK nextflow pipeline: https://github.com/isugifNF/GATK-flow

#### 23

Solution to modified basecalling: https://github.com/nanoporetech/dorado/issues/278

### May
#### 01

Sarek requires you to explicitly call the tools you want: https://nf-co.re/sarek/latest/docs/usage#which-variant-calling-tool-is-implemented-for-which-data-type
- Ran with: --tools 'freebayes,mutect2,mpileup,strelka2,manta,tiddit,cnvkit,control-freec' --aligner bwa-mem2

#### 03

*Meeting with Jared Simpson*
Phasing SNV information:
- Run workflow human variation to phase
- Workflow somatic mutation has a parameter for phasing

- The cram file has phased methylation
- Run the cram file through modkit to get the phased methylation information
- There is a switch to activate to get the different phasing

Informed WGS phasing with nanopore data
- The cram file required a phased vcf to generate a phased cram file
- Whatshap can phase germline variants
- We want a phased vcf, a set of long reads, and a set of somatic mutations
- No known program to figure out which reads associate with each mutation

Basecalling
- GPU queue has a GPU short queue
- Runs for less than 5hrs

IGV
- Script to samtools view the specific region within 10kb and then pull locally


#### 08

The Estonia collaboration:
- Used hg20.fa (HG38) for the reference genome

Download GATK data from: https://console.cloud.google.com/storage/browser/genomics-public-data/resources/broad/hg38/v0?pageState=(%22StorageObjectListTable%22:(%22f%22:%22%255B%255D%22))&prefix=&forceOnObjectsSortingFiltering=false

Download igenomes from: https://ewels.github.io/AWS-iGenomes/

Removed controlfreec from the Sarek testing. 

#### 09

Estonia collaboration directory: /.mounts/labs/reimandlab/private/generated_raw_data/Luhari_lung_2024-04-26

To download nextflow workflows from nf-core:

```sh
export NXF_SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/

export GITHUB_TOKEN=[personal_access_token]

cd /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/nextflow_pipelines_offline

nf-core download sarek --container-system singularity
nf-core download epi2me-labs/wf-somatic-variation --container-system singularity
nf-core download rnaseq --container-system singularity
nf-core download circdna --container-system singularity

```

### June
#### 03

To download VEP database and cache:
```
source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
conda activate vep_annotations

vep_install -a cf -s homo_sapiens -y GRCh38 -c /.mounts/labs/reimandlab/private/users/abahcheli/software/vep_ensembl/GRCh38/ --CONVERT --CACHE_VERSION 110
```

To download SNPeff database and cache:
```
aws s3 --no-sign-request sync s3://my-reference-data/cache/ensemblvep/GRCh38.105 .
```

#### 13
_Estonia_
- Identify best 2/3
- Separate by SNVs and Indels to visualize

- Subtract the NOMAD database and look at the variants

Original Dragen pipeline:
https://emea.illumina.com/products/by-type/informatics-products/basespace-sequence-hub/apps/dragen-somatic.html

Downloads for Dragen pipeline:
https://knowledge.illumina.com/software/on-premises-software/software-on-premises-software-troubleshooting-list/000007409


#### 14

Illumina exome panel: https://emea.support.illumina.com/downloads/enrichment-bed-files-hg38.html

#### 17

To merge SNVs and structural variants there was a hackathon by Sarek: https://github.com/nf-core/sarek/issues/738

Solution was to use just combine the variants without removing any.

For structural varuants, some tool was developed: svdb --merge
[https://github.com/J35P312/SVDB]


#### 21

To run PhyloWGS you need CNA information and VCF file information. They recommend using Battenberg for the CNA information.

Downloaded battenberg from: https://hub.docker.com/r/opengenomics/battenberg/tags
docker://opengenomics/battenberg:2.2.9

Battenberg reference files for HG38 downloaded from: https://ora.ox.ac.uk/objects/uuid:08e24957-7e76-438a-bd38-66c48008cf52


#### 25

Checked fast5 integrity using: https://github.com/rrwick/Fast5-to-Fastq/blob/master/fast5_integrity_check.py


### July
#### 03 
How many jobs ahead of my first queued job:
qstat -u '*' | grep -n abahcheli | grep qw | head -n1

#### 07
Number of methylation sites profiled (/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore/RLGS1-primary/RLGS1-primary/mod/5mC/bedMethyl/5mC.RLGS1-primary_normal.bed.gz): 30,165,006

Methlyation results described: https://nanoporetech.github.io/modkit/intro_bedmethyl.html

#### 10
Human genome annotations from here: https://www.ncbi.nlm.nih.gov/datasets/genome/GCF_000001405.40/

Check job details:
```sh
qstat -u 106386697
qstat -j 106386697
qacct -j 106386697
```

#### 19

For promoters, use 2000bp upstream and 200bp downstream of TSS: https://www.nature.com/articles/s41586-020-2135-x#Sec9


How can you compare the pathway-theme level information between species?
- Semantic similarity of GO terms?

Oliver's paper for replication timing of regions of the genome

#### 25

Extract promoters and enhancers from the hg38 genome
```
awk -F '\t' '$3 ~ /enhancer/' GCF_000001405.40_GRCh38.p14_genomic.gff >> /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/data/ref_data/GCF_000001405.40_GRCh38.p14_genomic.protein_coding_genes_enhancers.gff
```

### August 
#### 02 

Predict the GBM subtype using Sturgeon: https://github.com/marcpaga/sturgeon
- Requires mapping methylation nanopore reads to specific T2T genome: https://s3-us-west-2.amazonaws.com/human-pangenomics/T2T/CHM13/assemblies/analysis_set/chm13v2.0.fa.gz

#### 29

Save the final BAM file from sarek for each sample:
1. Check the "execution_trace" file for the final BAM file. Search for "BAM_VARIANT_CALLING_SOMATIC_STRELKA:STRELKA_SOMATIC" to find the directory that ran strelka on the recalibrated cram file.
2. Copy the final CRAM file and index to the final directory.

Remove the following sarek directories:
- work
- reference
- preprocessing


### September
#### 08

RNAseq analysis pipeline for GBM: https://github.com/nf-core/rnaseq

CPG traffic light(?)

### October
#### 16

Information on VEP annotations:
https://useast.ensembl.org/info/genome/variation/prediction/predicted_data.html

Mutation genes: ['TP53', 'IDH1', 'PTEN', 'ATRX', 'EGFR', 'NF1', 'TERT', 'PIK3CA', 'CIC', 'RB1', 'PIK3R1', 'FUBP1']
CNA genes: ['CDKN2A', 'EGFR', 'PDGFRA', 'CDK4', 'CDK6', 'PTEN', 'MDM2', 'MET', 'CCND2', 'MYCN', 'MDM4', 'ATRX']

### December 
#### 13

Oncoprint: just visualize the clearly functional variants and not non-coding variants
- Done

Is the EGFR-loss specific to the gene or the chromosome? 
It's regional: 20808345 nts are lost -> 95 genes including 4 CGC genes: SFRP4, IKZF1, EGFR, and ZNF479

Checkout battenburg and then t-leaves: https://github.com/Wedge-lab/battenberg

## 2025
### January
#### 10

HG38 blacklist regions from: https://github.com/Boyle-Lab/Blacklist/blob/master/lists/hg38-blacklist.v2.bed.gz

#### 23

Ensembl gff3 downloaded from FTP: https://useast.ensembl.org/info/data/ftp/index.html

SVs from WGS: 
- Manta
- Tiddit

### May
#### 27

Short-read ecDNA tools:
- AmpliconArchitect: https://github.com/jluebeck/AmpliconArchitect
- CircleMap: https://github.com/iprada/Circle-Map
- Starfish: https://github.com/bhklab/Starfish

Long-read ecDNA tools:
- ecc_finder: https://github.com/danis102/ecc_finder
- CoRAL: https://github.com/AmpliconSuite/CoRAL
- Decoil: https://github.com/madagiurgiu25/decoil-pre

### July 
#### 20

Look at the different types of features that could be on ecDNA: proteins, enhancers / promoters / TF-binding sites

Peter Dirks ATAC seq data for GBM

Identify journals with impact factors between 8 - 12 


SVs: Annotate the points of interest where there are many SVs (RLGS12)
- 

GMMs to estimate regions that are hyper/hypo-methylated

Exclude hypermutated samples from the P-value calculations for SNVs and SVs / CNAs
Also exclude genes that have zero mutations to avoid filling P-values with 1 

Estimate the time of the mutation before the diagnosis or surgery


Can we see the consequences of mutations in the phenotype?
- Validate some of the smaller mutations (eg EGFRv3, TERT promoter mutations, etc)


- Pull the top genes for IGV visualization


- Check the chromosome level event based on the location of chromosome arm
    - The size is small but the copy number is large


- Distinguish whether the amplfication / deletion is whole chr or just localized
    - Can we phase reads for CNAs of each haplotype

- Start looking loss of function based on different alterations / mutations

- Add the key genes to the evolutionary analysis
    - including the SVs if possible


- Are recurrent cells more stem-like?

- Graphaligner for tandom repeats




Snakemake process: 
Remember to unlock directory with: 
qstat | grep snake | awk '{print $1}' | xargs qdel
qstat | grep snake | grep -v job.g | awk '{print $1}' | xargs qdel

rm .snakemake/locks/*



https://www.biosciencewriters.com/Guidelines-for-Formatting-Gene-and-Protein-Names.aspx


for name in `ls`; do rm $name/*$name*en_summary.tsv;  done
for name in `cat tmp1.txt`; do qdel $name; done


scp -r -oProxyJump=<EMAIL> <EMAIL>:

qrsh -P reimandlab -l docker,docker_images="*broadinstitute/gatk*" -xd "-v /.mounts/labs/:/.mounts/labs/" df -h -t nfs


qrsh -P reimandlab -l h_vmem=2G -pe smp 30 -q int.q


```py

import numpy as np
import pandas as pd
import scipy.stats as stats
import statsmodels.stats.multitest as multitest

arr = np.array([1, 56,  2,  6,  3,  7,  5,  8,  2,  8])

arr2 = np.array(['yes', 'no', 'maybe', 'okay', ''])

tmp_df = pd.DataFrame([[1,5,2,6],[2,6,3,7],[4,7,4,3],[9,5,6,7],[9,3,1,4]], columns = ['col1', 'col2', 'col3', 'col4'])




import pandas as pd
from ActivePathways import ActivePathways

df = pd.read_csv("/Users/<USER>/Desktop/testing/hoxa10as_DE_raw_data.tsv", sep = "\t")
df.index = df.gene_name.to_numpy()

scores = df.loc[:,["OE_P.Value","KD_P.Value"]]
scores[pd.isnull(scores)] = 1
scores

scores_direction = df.loc[:,["OE_logFC","KD_logFC"]]
scores_direction[pd.isnull(scores_direction)] = 1
scores_direction

fname_GMT = "/Users/<USER>/Desktop/testing/hsap3.gmt"
res = ActivePathways(scores, merge_method = "Fisher",gmt = fname_GMT, cytoscape_file_tag = "TEST_")





l1 = list(map(lambda x: '="'.join(x.split()), tmp.strip("--").split(" --")))
[print(i + '"' ) for i in l1]



import boto3

# dynamo resource
dynamo_client = boto3.resource('dynamodb')
table = dynamo_client.Table('plasmids-information')
s3_client = boto3.resource('s3')



n = 0

score_dict = {}

with open("/Users/<USER>/Desktop/test.txt") as infile:
    for i, line in enumerate(infile):
        if line.startswith(">"):
            n = 0
        else:
            n += 1
            line = line.strip("\n")
            if line not in score_dict.keys():
                score_dict[line] = n
            else:
                score_dict[line] += n


results_dict = {val:key for key, val in score_dict.items()}


tmp = pd.read_csv("/Users/<USER>/Desktop/dgea_combined_results_edger.tsv", sep='\t')

res_df = []
for source in np.unique(tmp['data_source'].to_numpy()):
    res_df.append(tmp.loc[tmp['data_source'] == source,['PValue'],].copy())

res_df = pd.concat(res_df, axis=1)

```

Graph neural networks and transfer learning. 

Contectualized language models. 


__Can we use subclonal reconstruction to determine which antibodies would be most informative for mass cytometry?__

__What is the mistranslation rates in cancer? Single cell proteomics and genomic sequencing__




- ICGC data portal has some matrices from the PCAWG dataset
    - German brain initiative


__To Do__

- Cluster by expression (co-expression)
- Research shapero wilks and Walds tests

__Ion Channel Data Analysis__

Things to consider:
-Tumour infiltrating immune cells (TILs)
-Normal Tissue contamination of tumour sample
-

